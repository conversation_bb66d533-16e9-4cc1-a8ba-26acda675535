// 主要 JavaScript 功能
$(document).ready(function() {
    // 移动端菜单切换
    $('#mobile-menu-btn').click(function() {
        openMobileMenu();
    });

    // 关闭移动端菜单
    $('#mobile-menu-close').click(function() {
        closeMobileMenu();
    });

    // 打开移动端菜单函数
    function openMobileMenu() {
        $('#mobile-menu').removeClass('hidden');
        $('body').addClass('overflow-hidden'); // 防止背景滚动
    }

    // 关闭移动端菜单函数
    function closeMobileMenu() {
        $('#mobile-menu').addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }

    // 移动端菜单项点击事件
    $('#mobile-menu .mobile-nav-link').click(function() {
        // 如果是锚点链接，关闭菜单
        const href = $(this).attr('href');
        if (href && href.startsWith('#')) {
            closeMobileMenu();
        }

        // 记录点击的菜单项
        console.log('移动端导航到:', $(this).find('span').text().trim());
    });

    // 键盘事件支持
    $(document).keydown(function(e) {
        // ESC 键关闭移动端菜单
        if (e.key === 'Escape') {
            closeMobileMenu();
        }
    });

    // 防止菜单内容滚动时影响背景
    $('#mobile-menu').on('touchmove', function(e) {
        e.stopPropagation();
    });

    // 桌面导航菜单交互
    // $('.nav-link').click(function(e) {
    //     e.preventDefault();

    //     // 移除所有active类
    //     $('.nav-link').removeClass('active');

    //     // 为当前点击的链接添加active类
    //     $(this).addClass('active');

    //     // 可以在这里添加页面跳转逻辑
    //     const href = $(this).attr('href');
    //     console.log('导航到:', $(this).text().trim());
    // });

    // 导航菜单增强交互（CSS已处理悬浮效果）
    $('.nav-link').on('mouseenter', function() {
        if (!$(this).hasClass('active')) {
            $(this).addClass('nav-hover');
        }
    }).on('mouseleave', function() {
        $(this).removeClass('nav-hover');
    });

    // 同步桌面端和移动端导航状态
    function syncNavigationState(clickedElement) {
        const isDesktop = clickedElement.hasClass('nav-link');
        const isMobile = clickedElement.hasClass('mobile-nav-link');

        if (isDesktop || isMobile) {
            const index = clickedElement.closest('li').index();

            // 移除所有active类
            $('.nav-link, .mobile-nav-link').removeClass('active');

            // 设置对应索引的导航项为active
            $('.nav-link').eq(index).addClass('active');
            $('.mobile-nav-link').eq(index).addClass('active');
        }
    }

    // 平滑滚动
    $('a[href^="#"]').click(function(e) {
        e.preventDefault();
        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 80
            }, 800);
        }
    });

    // 导航栏滚动效果
    $(window).scroll(function() {
        const scrollTop = $(this).scrollTop();
        const nav = $('#navbar');
        
        if (scrollTop > 50) {
            nav.addClass('bg-black/80').removeClass('bg-[#030015]');
        } else {
            nav.addClass('bg-[#030015]').removeClass('bg-black/80');
        }
    });

    // 页面加载动画
    $('.fade-in-up').each(function(index) {
        $(this).delay(index * 100).queue(function() {
            $(this).addClass('fade-in-up').dequeue();
        });
    });

    // 按钮点击效果
    $('.btn-primary').click(function(e) {
        // 创建涟漪效果
        const button = $(this);
        const ripple = $('<span class="ripple"></span>');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.css({
            width: size,
            height: size,
            left: x,
            top: y
        });
        
        button.append(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    });

    // 响应式图片懒加载
    const images = $('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = $(entry.target);
                img.attr('src', img.data('src'));
                img.removeAttr('data-src');
                observer.unobserve(entry.target);
            }
        });
    });

    images.each(function() {
        imageObserver.observe(this);
    });

    // 表单验证
    $('form').submit(function(e) {
        e.preventDefault();
        
        const form = $(this);
        const inputs = form.find('input[required], textarea[required]');
        let isValid = true;
        
        inputs.each(function() {
            const input = $(this);
            const value = input.val().trim();
            
            if (!value) {
                input.addClass('border-red-500');
                isValid = false;
            } else {
                input.removeClass('border-red-500');
            }
        });
        
        if (isValid) {
            // 这里可以添加表单提交逻辑
            console.log('表单验证通过');
        }
    });

    // 工具提示
    $('[data-tooltip]').hover(
        function() {
            const tooltip = $('<div class="tooltip"></div>').text($(this).data('tooltip'));
            $('body').append(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.css({
                position: 'absolute',
                top: rect.top - tooltip.outerHeight() - 10,
                left: rect.left + rect.width / 2 - tooltip.outerWidth() / 2,
                background: 'rgba(0, 0, 0, 0.8)',
                color: 'white',
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '14px',
                zIndex: 1000
            });
        },
        function() {
            $('.tooltip').remove();
        }
    );

    // 返回顶部按钮
    const backToTop = $('<button id="back-to-top" class="fixed cursor-pointer bottom-8 right-8 bg-purple-500 hover:bg-purple-600 text-white p-3 rounded-full opacity-0 invisible z-10"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path></svg></button>');
    $('body').append(backToTop);

    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            backToTop.removeClass('opacity-0 invisible').addClass('opacity-100 visible');
        } else {
            backToTop.removeClass('opacity-100 visible').addClass('opacity-0 invisible');
        }
    });

    backToTop.click(function() {
        $('html, body').animate({ scrollTop: 0 }, 800);
    });

    // 视差滚动效果
    $(window).scroll(function() {
        const scrolled = $(this).scrollTop();
        const parallax = $('.parallax');
        const speed = 0.5;
        
        parallax.each(function() {
            const yPos = -(scrolled * speed);
            $(this).css('transform', `translateY(${yPos}px)`);
        });
    });

    // 数字计数动画
    $('.counter').each(function() {
        const counter = $(this);
        const target = parseInt(counter.text());
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.text(Math.floor(current));
        }, 16);
    });

    // 响应式处理
    function handleResize() {
        const width = $(window).width();
        
        if (width < 768) {
            // 移动端特殊处理
            $('.mobile-hidden').hide();
            $('.mobile-show').show();
        } else {
            // 桌面端特殊处理
            $('.mobile-hidden').show();
            $('.mobile-show').hide();
            $('#mobile-menu').addClass('hidden');
        }
    }

    $(window).resize(handleResize);
    handleResize(); // 初始化调用
});

// 页面加载完成后的初始化
$(window).on('load', function() {
    // 移除加载动画
    $('.loading').fadeOut();
    
    // 启动其他动画
    $('.animate-on-load').addClass('animated');
});
