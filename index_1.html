<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>并行宇宙 - PARALLEL UNIVERSE</title>
  <link href="/css/style.css" rel="stylesheet">
  <script src="/js/vendors/jquery-1.8.3.min.js"></script>
  <script src="/js/main.js" defer></script>
</head>

<body class=" min-h-screen bg-[#030015]">
  <!-- 主导航栏 -->
  <nav class="fixed top-0 left-0 right-0 z-50 bg-[#030015]  border-b border-[#3e3e3e]">
    <div class="md:max-w-10/12 mx-auto px-4 lg:px-0">
      <div class="flex items-center justify-between h-16 lg:h-[6.25rem]">
        <!-- Logo -->
        <div class="flex items-center space-x-3 ">
          <a href="/">
            <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
          </a>
        </div>

        <!-- 桌面导航菜单 -->
        <nav class="hidden lg:block h-full lg:flex-1 lg:ml-12" role="navigation" aria-label="主导航">
          <ul class="flex items-center h-full space-x-8">
            <li class="h-full">
              <a href="/" class="nav-link active flex items-center h-full px-6 relative">
                首页
                <span class="nav-indicator"></span>
              </a>
            </li>
            <li class="h-full">
              <a href="1.html" class="nav-link flex items-center h-full px-6 relative">
                服务
                <span class="nav-indicator"></span>
              </a>
            </li>
            <li class="h-full">
              <a href="" class="nav-link flex items-center h-full px-6 relative">
                策略
                <span class="nav-indicator"></span>
              </a>
            </li>
            <li class="h-full">
              <a href="" class="nav-link flex items-center h-full px-6 relative">
                案例
                <span class="nav-indicator"></span>
              </a>
            </li>
            <li class="h-full">
              <a href="" class="nav-link flex items-center h-full px-6 relative">
                FAQ
                <span class="nav-indicator"></span>
              </a>
            </li>
            <li class="h-full">
              <a href="" class="nav-link flex items-center h-full px-6 relative">
                关于我们
                <span class="nav-indicator"></span>
              </a>
            </li>
            <li class="h-full">
              <a href="" class="nav-link flex items-center h-full px-6 relative">
                联系我们
                <span class="nav-indicator"></span>
              </a>
            </li>
          </ul>
        </nav>

        <!-- 联系信息和二维码 -->
        <div class="hidden lg:flex items-center gap-10">
          <div class="text-white text-sm md:text-base">
            <span class="flex items-center gap-2">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
              </svg>
              400-681-8208
            </span>
          </div>
          <div class="w-[3.75rem] h-[3.75rem] border-2 border-[#725e9b] rounded-md flex items-center justify-center cursor-pointer group relative">
            <img src="/img/qr.jpg" class="w-full h-full rounded-md opacity-90 group-hover:opacity-100 transition-opacity duration-300" alt="">
            <div class="border-2 border-[#725e9b] bg-[#272636] absolute top-[120%] min-w-[11.125rem] p-5 hidden group-hover:block">
              <img src="/img/qr_dow.jpg" class="max-w-full object-cover" alt="">
            </div>
          </div>
        </div>

        <!-- 移动端菜单按钮 -->
        <button id="mobile-menu-btn" class="lg:hidden text-white p-2 cursor-pointer">
          <svg t="1752221010071" class="icon w-6 h-6" viewBox="0 0 1024 1024" version="1.1"
            xmlns="http://www.w3.org/2000/svg" p-id="8317" width="200" height="200">
            <path
              d="M153.6 256h716.8a51.2 51.2 0 0 0 0-102.4H153.6a51.2 51.2 0 0 0 0 102.4zM665.6 460.8H153.6a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4zM460.8 768H153.6a51.2 51.2 0 0 0 0 102.4h307.2a51.2 51.2 0 0 0 0-102.4z"
              fill="#ffffff" p-id="8318"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div id="mobile-menu" class="lg:hidden fixed inset-0 backdrop-blur-xl z-50 hidden h-lvh bg-[#030015]">
      <!-- 菜单头部 -->
      <div class="flex items-center justify-between px-4 border-b border-white/10 h-16">
        <div class="flex items-center space-x-3 ">
          <a href="/">
            <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
          </a>
        </div>
        <button id="mobile-menu-close"
          class="text-white p-2 hover:bg-white/10 rounded-full transition-colors duration-200">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- 菜单内容 -->
      <div class="flex-1 flex flex-col justify-center">
        <nav class="py-8" role="navigation" aria-label="移动端导航">
          <ul class="space-y-0">
            <li>
              <a href="/"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">首页</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/services"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">服务</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/guide"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">策略</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/cases"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">案例</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/faq"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">FAQ</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/about"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">关于我们</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/contact"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">联系我们</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
          </ul>
        </nav>
      </div>

      <!-- 菜单底部联系信息 -->
      <div class="p-8 border-t border-white/10">
        <div class="flex items-center justify-center space-x-4 text-white">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
          </svg>
          <span class="text-xl font-medium">400-681-8208</span>
        </div>
      </div>
    </div>
  </nav>
  <!-- 主要内容区域 -->
  <main class="pt-16 lg:pt-20">
    <!-- 获取优化方案 -->
    <section class="md:min-h-screen flex items-center flex-col justify-center px-4 lg:px-8 pb-12 overflow-hidden banner-container">
      <div class="container mx-auto text-center">
        <div class="max-w-4xl mx-auto pt-16 md:max-w-max">
          <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
            <span class="text_1">
              <span class="first-letter">G</span>enerative
            </span>
            <br class="md:hidden">
            <span class="text_1">
              <span class="first-letter">E</span>ngine
            </span>
            <br class="md:hidden">
            <span class="text_1">
              <span class="first-letter">O</span>ptimization
            </span>
          </h1>
          <p class="text-lg md:text-xl lg:text-2xl text-white mb-8 leading-relaxed">
            GEO让AI生成的内容更“好用”
          </p>
          <div class="flex justify-center w-[12.5rem] md:w-[18.75rem] mx-auto">
            <button
              class="border-2 border-white hover:border-white/50 w-full md:text-xl md:py-3.5 text-white px-4 py-3 rounded-full transition-all duration-300 hover:bg-white/10 text-sm cursor-pointer">
              获取AI优化方案
            </button>
          </div>
        </div>
      </div>
      <div class="md:mt-10 wave-container bg-none">
        <img src="/img/banner-logo-x.png" class="mx-auto" alt="">
        <div class="hidden">
          <div class="wave wave-1"></div>
          <div class="wave wave-2"></div>
          <div class="wave wave-3"></div>
          <div class="wave wave-4"></div>
        </div>
      </div>
      
    </section>

    <!-- 关于 G E O -->
    <section class="-mt-16 lg:px-8">
      <div class="px-4">
        <div class="geo-card-container container mx-auto border border-[#725e9b] rounded-xl p-3 bg-black/20">
          <div class="geo-content-area text-center bg-black/90 rounded-xl">
            <div class="flex justify-center max-w-[9.375rem] mx-auto pt-10 mb-6">
              <span
                class="geo-tag w-full border-2 border-[#412d5a] text-[#efc3ff] text-sm hover:border-white/50 px-4 py-3 rounded-full transition-all duration-300 hover:bg-white/10">
                关于 G E O
              </span>
            </div>

            <h2 class="text-2xl md:text-4xl lg:text-5xl font-bold mb-6 vertical-gradient-text">
              什么是GEO生成引擎优化
            </h2>
            <p class="text-sm max-w-2xl mx-auto leading-7 text-[#a9a8b9]">
              是指有针对性地创作和优化互联网上的内容，帮助这些内容在用户使用生成式<strong class="text-white">AI应用</strong>时（如ChatGPT或DeepSeek） ，
              <strong class="text-white">获得更好的展示效果和更高的可见性。</strong><br />
              <strong class="text-white">上海源易</strong>在传统SEO领域有超过20年技术经验，也是<strong
                class="text-white">GEO在中国的倡行和领导者。</strong>
            </p>
          </div>
        </div>
      </div>
      <div class="bg-[url('/img/banner-wg-m.png')] bg-no-repeat bg-cover bg-center pt-5 pb-20">
        <div class="w-[10.875rem] h-[10.875rem] mx-auto">
          <img src="/img/banner-bg-ai.png" alt="" class="w-full object-cover" />
        </div>
      </div>
    </section>
    <!-- GEO优化方向 -->
    <section class="lg:px-8">
      <div class="container mx-auto">
        <!-- 卡片容器 -->
        <div class="geo-optimization-card relative overflow-hidden rounded-tl-3xl rounded-tr-3xl bg-[#f5f5f5] py-8 ">
          <!-- 标签 -->
          <div class="flex justify-center mb-6">
            <span
              class="inline-flex items-center px-6 py-2 rounded-full border border-[#725e9b]text-[#725e9b] text-sm font-medium">
              GEO核心方向
            </span>
          </div>

          <!-- 标题 -->
          <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-center text-[#111111]">
            GEO的核心优化方向
          </h2>

          <!-- 优化方向网格 -->
          <div class="relative flex flex-wrap md:grid-cols-2 lg:grid-cols-3">
            <!-- 渐变分割线 -->
            <!-- 垂直分割线 - 两列布局时的中间分割线 -->
            <div
              class=" md:block lg:hidden absolute top-0 bottom-0 left-1/2 transform -translate-x-1/2 gradient-divider-vertical">
            </div>

            <!-- 垂直分割线 - 三列布局时的分割线 -->
            <div
              class="hidden lg:block absolute top-0 bottom-0 left-1/3 transform -translate-x-1/2 gradient-divider-vertical">
            </div>
            <div
              class="hidden lg:block absolute top-0 bottom-0 left-2/3 transform -translate-x-1/2 gradient-divider-vertical">
            </div>


            <!-- 水平分割线 - 上下两行之间 -->
            <!-- <div class=" md:block absolute left-0 right-0 top-1/2 transform -translate-y-1/2 gradient-divider-horizontal"></div> -->

            <!-- 三列布局时的额外水平分割线 -->
            <div
              class=" lg:block absolute left-0 right-0 top-1/3 transform -translate-y-1/2 gradient-divider-horizontal md:top-1/2">
            </div>
            <div
              class=" lg:hidden absolute left-0 right-0 top-2/3 transform -translate-y-1/2 gradient-divider-horizontal">
            </div>


            <!-- 提升内容质量 -->
            <div class="optimization-item group fade-in-up delay-200 px-2">
              <div class="flex flex-col transition-all duration-30 ">
                <div class="w-8 h-8 mb-4 flex items-center justify-center rounded-full transition-colors duration-300">
                  <img src="/img/zhinengyouhua.png" alt="提升内容质量" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4">提升内容质量</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left">
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    增强专业性（如生成符合医学规范的诊断建议）。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 模型调优 -->
            <div class="optimization-item group fade-in-up delay-200 px-4">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center">
                  <img src="/img/_moxingguanli.png" alt="模型调优" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4">模型调优</h3>
                <ul class="text-sm text-gray-600 space-y-2 text-left">
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    针对特定任务或领域，用垂直数据对预训练模型进行二次训练，提升专业性（例如医疗报告生成）。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    调整模型超参数（如温度值、Top-k采样）以平衡生成内容的创造性和准确性。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 生成控制 -->
            <div class="optimization-item group fade-in-up delay-300 px-4">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center">
                  <img src="/img/xitongkongzhi.png" alt="生成控制" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4">生成控制</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left">
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    通过条件约束（如关键词、格式模板）控制输出结构。例如，生成广告文案时限定产品卖点和语气。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    结合用户交互反馈（如用户点击、修正）动态调整生成策略。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 后处理优化 -->
            <div class="optimization-item group fade-in-up delay-400 px-4">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center">
                  <img src="/img/shezhi-5.png" alt="后处理优化" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4">后处理优化</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left">
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    通过规则或小模型对生成内容进行筛选，剔除低质量或不符合要求的结果。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    对生成结果进行二次加工（如调整语气、简化语言）以适配不同场景。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 效率优化 -->
            <div class="optimization-item group fade-in-up delay-500 px-4">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center rounded-full ">
                  <img src="/img/xiaoshuai-2.png" alt="效率优化" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4">效率优化</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left">
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    通过剪枝（Pruning）、量化（Quantization）等技术压缩模型体积，提升推理速度。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    利用GPU/TPU等专用硬件或分布式计算优化生成效率。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 评估与迭代 -->
            <div class="optimization-item group fade-in-up delay-600 px-4">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center rounded-full">
                  <img src="/img/pinggumoban.png" alt="评估与迭代" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4">评估与迭代</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left">
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    基础指标（BLEU、ROUGE）结合业务指标（转化率）。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    标注错误生成结果反哺模型迭代。
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- GEO生成引擎优化领航者 -->
    <section class="geo-leader-section lg:px-8 overflow-hidden bg-[url('/img/bj-m.jpg')] bg-no-repeat bg-bottom bg-size-[100%] h-[9.375rem] px-4">
      <!-- 内容容器 -->
      <div class="container mx-auto relative z-10 h-full">
        <div class="flex items-center justify-center gap-4 h-full">
          <!-- 左侧书籍图片 -->
          <div class="flex-shrink-0 w-28 flex justify-center lg:justify-start">
            <img src="/img/book_pc.png" alt="GEO优化书籍" class="w-full">
          </div>

          <!-- 右侧内容 -->
          <div class="flex-1 text-center lg:text-left">
            <!-- 主标题 -->
            <h2 class="text-xl md:text-5xl lg:text-6xl font-bold mb-4 vertical-gradient-text ">
              "GEO" 生成引擎优化领航者
            </h2>

            <!-- CTA按钮 -->
            <div class="flex justify-center lg:justify-start">
              <button class="geo-cta-button group relative overflow-hidden px-12 py-2 bg-white text-[#111111] font-bold text-lg rounded-md hover:bg-gray-100 transition-all duration-300 hover:shadow-purple-500/25">
                <span class="relative z-10">获取AI优化方案</span>
                <div class="absolute inset-0 rounded-full border-2 border-transparent bg-gradient-to-r from-purple-500 to-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="px-4 bg-[#030015] pb-[3.25rem]">
      <div class="flex justify-center max-w-[9.375rem] mx-auto pt-10 mb-6">
        <span class="geo-tag w-full border-2 border-[#412d5a] text-[#efc3ff] text-sm hover:border-white/50 px-4 py-3 rounded-full transition-all duration-300 hover:bg-white/10 text-center">
          GEO和SEO对比
        </span>
      </div>
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-center vertical-gradient-text mb-6">
        GEO与SEO的本质区别
      </h2>
      <div class="w-full">
        <img src="/img/comparison.png" class="max-w-full" alt="" />
      </div>
    </section>

    <!-- GEO的价值和必要性 -->
    <section class="geo-value-section py-16 px-4 lg:px-8 bg-gray-50">
      <div class="container mx-auto max-w-4xl">
        <!-- 标签按钮 -->
        <div class="flex justify-center mb-8">
          <span class="inline-block px-6 py-2 bg-white text-purple-600 rounded-full border border-purple-200 text-sm font-medium">
            GEO价值
          </span>
        </div>

        <!-- 主标题 -->
        <h2 class="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-6">
          GEO的价值和必要性
        </h2>

        <!-- 描述文字 -->
        <div class="text-center text-gray-600 mb-12 space-y-4">
          <p>GEO价值体现在能够持续不断地生成高质量人工智能（AIGC）技术的内容来提高整体产品的</p>
          <p>质量文本、图像、音频、视频等主要内容的管理。</p>
          <p>如何让这些引擎更精准、高效、可控的输出更多优质内容，是我们需要关注的核心问题。</p>
        </div>

        <!-- 价值卡片 -->
        <div class="space-y-6">
          <!-- 卡片1 -->
          <div class="geo-value-card bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0">
                <span class="comparison-text text-2xl font-bold">1</span>
              </div>
              <div class="flex-1">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提升生成内容的质量与可靠性</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4490"><path d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z" fill="#0c0101" p-id="4491"></path></svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1">减少错误输出：</h4>
                      <p class="text-gray-600 text-sm">通过优化模型参数、数据预处理和后处理流程，减少生成内容中的逻辑错误、事实错误和格式问题。</p>
                    </div>
                  </div>
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4490"><path d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z" fill="#0c0101" p-id="4491"></path></svg>
                    </div>
                      <h4 class="font-semibold text-gray-900 mb-1">增强专业性：</h4>
                      <p class="text-gray-600 text-sm">在专业领域（如医疗、法律、金融）中优化生成内容，确保内容符合行业标准和专业要求（例如医疗诊断的准确性）。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 卡片2 -->
          <div class="geo-value-card bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-600 text-white font-bold text-lg rounded">2</span>
              </div>
              <div class="flex-1">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提高生成效率与成本效益</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-3">
                    <div class="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1">降低计算成本：</h4>
                      <p class="text-gray-600 text-sm">通过模型压缩、量化和推理优化技术，减少生成所需的计算资源和时间成本，特别是在大规模部署时节省成本。</p>
                    </div>
                  </div>
                  <div class="flex items-start gap-3">
                    <div class="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1">加速迭代速度：</h4>
                      <p class="text-gray-600 text-sm">优化训练和微调流程，使模型能够更快地适应新数据和新任务（如产品文案的快速生成）。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 卡片3 -->
          <div class="geo-value-card bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-600 text-white font-bold text-lg rounded">3</span>
              </div>
              <div class="flex-1">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提升生成内容的质量与可靠性</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-3">
                    <div class="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1">减少错误输出：</h4>
                      <p class="text-gray-600 text-sm">通过优化模型参数、数据预处理和后处理流程，减少生成内容中的逻辑错误、事实错误和格式问题。</p>
                    </div>
                  </div>
                  <div class="flex items-start gap-3">
                    <div class="w-2 h-2 bg-black rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1">增强专业性：</h4>
                      <p class="text-gray-600 text-sm">在专业领域（如医疗、法律、金融）中优化生成内容，确保内容符合行业标准和专业要求（例如医疗诊断的准确性）。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

  </main>


</body>

</html>