<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <!-- 高DPI显示优化 -->
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>关于我们 - PARALLEL UNIVERSE</title>
    <link rel="shortcut icon" href="/favicon.ico">
    </link>
    <!-- 字体图标 -->
    <link rel="stylesheet" href="/css/iconfont.css" />
    <link href="/css/style.css" rel="stylesheet">
</head>

<body class="min-h-screen">
    <!-- 主导航栏 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-[#030015]  border-b border-[#3e3e3e]" id="navbar">
        <div class="md:max-w-10/12 mx-auto px-4 lg:px-0">
            <div class="flex items-center justify-between h-16 lg:h-[6.25rem]">
                <!-- Logo -->
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>

                <!-- 桌面导航菜单 -->
                <nav class="hidden lg:block h-full lg:flex-1 lg:ml-12" role="navigation" aria-label="主导航">
                    <ul class="flex items-center h-full space-x-8">
                        <li class="h-full flex items-center justify-center">
                            <a href="/" class="nav-link flex items-center px-5">
                                首页
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/services" class="nav-link flex items-center px-5">
                                服务
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                策略
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                案例
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                FAQ
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5 active">
                                关于我们
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                联系我们
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- 联系信息和二维码 -->
                <div class="hidden lg:flex items-center gap-10">
                    <div class="text-white text-sm md:text-base">
                        <span class="flex items-center gap-2">
                            <i class="iconfont icon-weibiaoti- iconfont-telephone"></i>
                            ************
                        </span>
                    </div>
                    <div
                        class="w-[3.75rem] h-[3.75rem] border-2 border-[#725e9b] rounded-xl flex items-center justify-center cursor-pointer group relative">
                        <i class="iconfont icon-erweima text-white rq-telephone"></i>
                        <div
                            class="border-2 border-[#725e9b] bg-[#272636] absolute top-[120%] min-w-[11.125rem] p-5 hidden group-hover:block">
                            <img src="/img/qr_dow.jpg" class="max-w-full object-cover" alt="">
                        </div>
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="lg:hidden text-white p-2 cursor-pointer">
                    <svg t="1752221010071" class="icon w-6 h-6" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="8317" width="200" height="200">
                        <path
                            d="M153.6 256h716.8a51.2 51.2 0 0 0 0-102.4H153.6a51.2 51.2 0 0 0 0 102.4zM665.6 460.8H153.6a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4zM460.8 768H153.6a51.2 51.2 0 0 0 0 102.4h307.2a51.2 51.2 0 0 0 0-102.4z"
                            fill="#ffffff" p-id="8318"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="lg:hidden fixed inset-0 backdrop-blur-xl z-50 hidden h-lvh bg-[#030015]">
            <!-- 菜单头部 -->
            <div class="flex items-center justify-between px-4 border-b border-white/10 h-16">
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>
                <button id="mobile-menu-close"
                    class="text-white p-2 hover:bg-white/10 rounded-full transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- 菜单内容 -->
            <div class="flex-1 flex flex-col justify-center">
                <nav class="py-8" role="navigation" aria-label="移动端导航">
                    <ul class="space-y-0">
                        <li>
                            <a href="/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">首页</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/services"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">服务</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/guide"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">策略</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/cases"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">案例</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/faq"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">FAQ</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/about"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">关于我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/contact"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">联系我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 菜单底部联系信息 -->
            <div class="p-8 border-t border-white/10">
                <div class="flex items-center justify-center space-x-4 text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
                    </svg>
                    <span class="text-xl font-medium">************</span>
                </div>
            </div>
        </div>
    </header>

    <main class="pt-16 md:pt-[6.25rem] min-h-screen bg-[#0d0b1e] text-white md:pb-10">
        <section class="relative h-full">
            <span
                class="absolute w-full h-full bg-[url(/img/about_bg_2.png)] bg-no-repeat bg-[left_top] bg-cover z-0 md:bg-auto"></span>
            <span
                class="absolute w-full h-full bg-[url(/img/about_bg_1.png)] bg-no-repeat bg-[right_bottom] bg-size-[100%] z-0 md:bg-size-[62.5rem] "></span>
            <div class="w-11/12 mx-auto md:w-10/12 relative z-10 h-lvh md:h-lvh">
                <div class="w-[12.5rem] mx-auto pt-[3.75rem] mb-[3rem] md:w-[18.75rem] md:mb-[6.25rem] md:pt-[5.125rem]">
                    <img src="/img/title_logo.png" alt="" class="w-full object-cover">
                </div>
                <div class="flex flex-col justify-between md:flex-row md:gap-x-52 md:items-start">
                    <div class="flex flex-col md:max-w-3xl">
                        <h1 class="text-4xl font-bold mb-8 md:mb-[3.125rem]">
                            我们是谁？
                        </h1>
                        <div class="text-[#dddddd] leading-relaxed">
                            <p>
                                我们是一家专注于数据智能与人工智能应用的创新科技公司。公司是生成引擎优化（GEO, Generative Engine
                                Optimization）技术在国内的领导者，致力于帮助品牌在AI生成内容时代获得更高的曝光与引用。通过结合语义优化策略、结构化内容部署及多平台数据联动，我们提升品牌在大模型中的可见度与可信度，推动内容“被生成”的效率与质量。
                            </p>    
                            <p>此外，我们也提供基于大模型能力的AI客服系统与行业智能体解决方案，助力企业实现自动化、智能化客户触达与服务升级，全面提升运营效率与用户体验。
                            </p>
                        </div>
                    </div>

                    <!-- 统计数据卡片 -->
                    <div class="grid grid-cols-2 md:grid-cols-2 gap-6 mt-12 md:w-1/3 md:mt-0">
                        <!-- 服务客户卡片 -->
                        <div class="border border-[#725e9b] rounded-2xl p-8 text-center md:w-[11.25rem] md:h-[8.75rem]">
                            <div class="vertical-gradient-text text-3xl md:text-5xl font-bold mb-4 md:flex md:items-start md:justify-center">
                                100<sup class="text-2xl md:text-4xl vertical-gradient-text">+</sup>
                            </div>
                            <div class="text-[#dddddd] text-lg md:text-base font-medium">
                                服务客户
                            </div>
                        </div>

                        <!-- 经典案例卡片 -->
                        <div class="border border-[#725e9b] rounded-2xl p-8 text-center md:w-[11.25rem] md:h-[8.75rem]">
                            <div class="vertical-gradient-text text-3xl md:text-5xl font-bold mb-4 md:flex md:items-start md:justify-center">
                                80<sup class="text-2xl md:text-4xl vertical-gradient-text">+</sup>
                            </div>
                            <div class="text-[#dddddd] text-lg md:text-base font-medium">
                                经典案例
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>
        <section class="-mt-16 relative z-10 pb-10 md:pt-10 md:-mt-20">
            <div class="w-11/12 mx-auto md:w-10/12">
                <div class="mb-8 md:mb-10">
                    <h2 class="text-2xl font-bold text-white md:text-4xl">主要产品</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16 md:gap-5">

                    <div
                        class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-5 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="flex w-[4rem] h-[4rem] bg-[#343045] rounded-2xl items-center justify-center">
                                <i class="iconfont icon-zhinengyouhua text-4xl vertical-gradient-text"></i>
                            </span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">生成引擎优化（GEO）服务</h3>

                        <!-- 优势列表 -->
                        <div class="text-[#8a8f99] leading-relaxed">
                            <p>
                                通过分析大模型生成机制，优化品牌内容的结构、语义与分发路径，提升其在AI生成场景中的可见度与引用率。帮助企业在内容搜索、问答推荐等智能生成结果中“被优先生成”，抢占AI流量入口。
                            </p>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-5 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="flex w-[4rem] h-[4rem] bg-[#343045] rounded-2xl items-center justify-center">
                                <i class="iconfont icon-kefu text-4xl vertical-gradient-text"></i>
                            </span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">AI客服与行业智能体</h3>

                        <!-- 优势列表 -->
                        <div class="text-[#8a8f99] leading-relaxed">
                            <p>
                                基于大模型构建企业专属智能客服与智能体，具备自然语言理解、多轮对话与场景处理能力。支持多渠道接入，实现高效、智能、7×24小时不间断的客户服务与业务。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <footer
        class="py-10 bg-[url(/img/footer_bg.png)] bg-no-repeat bg-[center_bottom] flex flex-col items-center gap-y-8 md:pt-20 bg-[#030015] border-t border-[#343045]">
        <a href="">
            <img src="/img/footer_logo.png" alt="" />
        </a>
        <p class="uppercase text-[#bfc4ce] text-sm">
            © 2025 yuanyi, Inc. All rights reserved
        </p>
    </footer>
    <script src="/js/vendors/jquery-1.8.3.min.js"></script>
    <script src="/js/main.js"></script>

</body>

</html>