<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <!-- 高DPI显示优化 -->
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title>案例 - PARALLEL UNIVERSE</title>
    <link rel="shortcut icon" href="/favicon.ico">
    </link>
    <!-- 字体图标 -->
    <link rel="stylesheet" href="/css/iconfont.css" />
    <link href="/css/style.css" rel="stylesheet">
</head>

<body class="min-h-screen">
    <!-- 主导航栏 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-[#030015]  border-b border-[#3e3e3e]" id="navbar">
        <div class="md:max-w-10/12 mx-auto px-4 lg:px-0">
            <div class="flex items-center justify-between h-16 lg:h-[6.25rem]">
                <!-- Logo -->
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>

                <!-- 桌面导航菜单 -->
                <nav class="hidden lg:block h-full lg:flex-1 lg:ml-12" role="navigation" aria-label="主导航">
                    <ul class="flex items-center h-full space-x-8">
                        <li class="h-full flex items-center justify-center">
                            <a href="/" class="nav-link flex items-center px-5">
                                首页
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/services" class="nav-link flex items-center px-5">
                                服务
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                策略
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                案例
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5 active">
                                FAQ
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                关于我们
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                联系我们
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- 联系信息和二维码 -->
                <div class="hidden lg:flex items-center gap-10">
                    <div class="text-white text-sm md:text-base">
                        <span class="flex items-center gap-2">
                            <i class="iconfont icon-weibiaoti- iconfont-telephone"></i>
                            ************
                        </span>
                    </div>
                    <div
                        class="w-[3.75rem] h-[3.75rem] border-2 border-[#725e9b] rounded-xl flex items-center justify-center cursor-pointer group relative">
                        <i class="iconfont icon-erweima text-white rq-telephone"></i>
                        <div
                            class="border-2 border-[#725e9b] bg-[#272636] absolute top-[120%] min-w-[11.125rem] p-5 hidden group-hover:block">
                            <img src="/img/qr_dow.jpg" class="max-w-full object-cover" alt="">
                        </div>
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="lg:hidden text-white p-2 cursor-pointer">
                    <svg t="1752221010071" class="icon w-6 h-6" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="8317" width="200" height="200">
                        <path
                            d="M153.6 256h716.8a51.2 51.2 0 0 0 0-102.4H153.6a51.2 51.2 0 0 0 0 102.4zM665.6 460.8H153.6a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4zM460.8 768H153.6a51.2 51.2 0 0 0 0 102.4h307.2a51.2 51.2 0 0 0 0-102.4z"
                            fill="#ffffff" p-id="8318"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="lg:hidden fixed inset-0 backdrop-blur-xl z-50 hidden h-lvh bg-[#030015]">
            <!-- 菜单头部 -->
            <div class="flex items-center justify-between px-4 border-b border-white/10 h-16">
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>
                <button id="mobile-menu-close"
                    class="text-white p-2 hover:bg-white/10 rounded-full transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- 菜单内容 -->
            <div class="flex-1 flex flex-col justify-center">
                <nav class="py-8" role="navigation" aria-label="移动端导航">
                    <ul class="space-y-0">
                        <li>
                            <a href="/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">首页</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/services"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">服务</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/guide"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">策略</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/cases"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">案例</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/faq"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">FAQ</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/about"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">关于我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/contact"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">联系我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 菜单底部联系信息 -->
            <div class="p-8 border-t border-white/10">
                <div class="flex items-center justify-center space-x-4 text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
                    </svg>
                    <span class="text-xl font-medium">************</span>
                </div>
            </div>
        </div>
    </header>

    <main class="pt-16 md:pt-[6.25rem] min-h-screen bg-[#0d0b1e] text-white md:pb-10">
        <div class="relative mb-[1.5625rem] md:mb-16 text-center">
            <picture>
                <source media="(max-width: 600px)" srcset="/img/fqa_bg_m.jpg">
                <source media="(min-width: 601px)" srcset="/img/fqa_bg.jpg">
                <img src="/img/fqa_bg.jpg" alt="" class="w-full object-cover">
            </picture>
            <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <h1 class="text-5xl font-bold text-center mb-3.5 SourceHanSans_Regular md:text-8xl md:mb-8">
                    案例
                </h1>
                <p class="text-lg">
                    精品案例展示
                </p>
            </div>
        </div>
        <div class="w-11/12 mx-auto md:w-10/12">

            <!-- 案例展示网格 -->
            <div class="grid grid-cols-1 gap-5 relative z-10 md:grid-cols-3">
                <!-- 案例卡片 1 -->
                <div
                    class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3
                            class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>

                <!-- 案例卡片 2 -->
                <div
                    class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3
                            class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>

                <div
                    class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3
                            class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>

                <div
                    class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3
                            class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>
                <div
                    class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3
                            class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>
                <div
                    class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3
                            class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>
            </div>
            <!-- 分页 -->
            <div class="pagination-container flex justify-center items-center gap-2 py-8">
                <!-- 上一页按钮 -->
                <button
                    class="prev-btn w-16 h-12 bg-[#2a2438] border border-[#423e52] rounded-lg flex items-center justify-center text-white hover:bg-[#3a3448] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed -rotate-180 text-2xl cursor-pointer">
                    <i class="iconfont icon-fangxiang-jiantouyou"></i>
                </button>

                <!-- 页码按钮 -->
                <button
                    class="page-btn w-12 h-12 bg-[#6b46c1] border border-[#7c3aed] rounded-lg flex items-center justify-center text-white font-medium hover:bg-[#7c3aed] transition-colors duration-200 cursor-pointer"
                    data-page="1">
                    1
                </button>
                <button
                    class="page-btn w-12 h-12 bg-[#2a2438] border border-[#423e52] rounded-lg flex items-center justify-center text-white font-medium hover:bg-[#3a3448] transition-colors duration-200 cursor-pointer"
                    data-page="2">
                    2
                </button>
                <button
                    class="page-btn w-12 h-12 bg-[#2a2438] border border-[#423e52] rounded-lg flex items-center justify-center text-white font-medium hover:bg-[#3a3448] transition-colors duration-200 cursor-pointer"
                    data-page="3">
                    3
                </button>

                <!-- 省略号 -->
                <span
                    class="w-12 h-12 bg-[#2a2438] border border-[#423e52] rounded-lg flex items-center justify-center text-white font-medium hover:bg-[#3a3448] transition-colors duration-200 cursor-pointer">
                    ...
                </span>

                <button
                    class="page-btn w-12 h-12 bg-[#2a2438] border border-[#423e52] rounded-lg flex items-center justify-center text-white font-medium hover:bg-[#3a3448] transition-colors duration-200 cursor-pointer"
                    data-page="10">
                    10
                </button>

                <!-- 下一页按钮 -->
                <button
                    class="next-btn w-16 h-12 bg-[#2a2438] border border-[#423e52] rounded-lg flex items-center justify-center text-white hover:bg-[#3a3448] transition-colors duration-200 text-2xl cursor-pointer">
                    <i class="iconfont icon-fangxiang-jiantouyou"></i>
                </button>
            </div>
        </div>
    </main>
    <footer
        class="py-10 bg-[url(/img/footer_bg.png)] bg-no-repeat bg-[center_bottom] flex flex-col items-center gap-y-8 md:pt-20 bg-[#030015] border-t border-[#343045]">
        <a href="">
            <img src="/img/footer_logo.png" alt="" />
        </a>
        <p class="uppercase text-[#bfc4ce] text-sm">
            © 2025 yuanyi, Inc. All rights reserved
        </p>
    </footer>
    <script src="/js/vendors/jquery-1.8.3.min.js"></script>
    <script src="/js/main.js"></script>
    <script>
        // 分页功能
        $(document).ready(function () {
            const $pagination = $('.pagination-container');
            const $pageButtons = $pagination.find('.page-btn');
            const $prevBtn = $pagination.find('.prev-btn');
            const $nextBtn = $pagination.find('.next-btn');

            let currentPage = 1;
            const totalPages = 10;

            // 页码按钮点击事件
            $pageButtons.on('click', function () {
                const page = parseInt($(this).text());
                if (page !== currentPage) {
                    setCurrentPage(page);
                }
            });

            // 上一页按钮
            $prevBtn.on('click', function () {
                if (currentPage > 1) {
                    setCurrentPage(currentPage - 1);
                }
            });

            // 下一页按钮
            $nextBtn.on('click', function () {
                if (currentPage < totalPages) {
                    setCurrentPage(currentPage + 1);
                }
            });

            // 设置当前页面
            function setCurrentPage(page) {
                currentPage = page;

                // 更新按钮状态
                $pageButtons.removeClass('bg-[#6b46c1] border-[#7c3aed]').addClass('bg-[#2a2438] border-[#423e52]');
                $pageButtons.filter(`[data-page="${page}"]`).removeClass('bg-[#2a2438] border-[#423e52]').addClass('bg-[#6b46c1] border-[#7c3aed]');

                // 更新导航按钮状态
                $prevBtn.prop('disabled', currentPage === 1);
                $nextBtn.prop('disabled', currentPage === totalPages);

                // 这里可以添加加载新内容的逻辑
                console.log('切换到第', page, '页');
            }

            // 初始化
            setCurrentPage(1);
        });
    </script>
</body>

</html>