<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
  <!-- 高DPI显示优化 -->
  <meta name="format-detection" content="telephone=no">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- 防止缩放模糊的关键设置 -->
  <meta name="msapplication-tap-highlight" content="no">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <title>并行宇宙 - PARALLEL UNIVERSE</title>
  <link rel="shortcut icon" href="/favicon.ico">
  </link>
  <link href="/css/swiper-bundle.min.css" rel="stylesheet">
  <!-- 字体图标 -->
  <link rel="stylesheet" href="/css/iconfont.css" />
  <link href="/css/style.css" rel="stylesheet">



</head>

<body class=" min-h-screen bg-[#030015]">
  <!-- 主导航栏 -->
  <header class="fixed top-0 left-0 right-0 z-50 bg-[#030015]  border-b border-[#3e3e3e]" id="navbar">
    <div class="md:max-w-10/12 mx-auto px-4 lg:px-0">
      <div class="flex items-center justify-between h-16 lg:h-[6.25rem]">
        <!-- Logo -->
        <div class="flex items-center space-x-3 ">
          <a href="/">
            <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
          </a>
        </div>

        <!-- 桌面导航菜单 -->
        <nav class="hidden lg:block h-full lg:flex-1 lg:ml-12" role="navigation" aria-label="主导航">
          <ul class="flex items-center h-full space-x-8">
            <li class="h-full flex items-center justify-center">
              <a href="/" class="nav-link active flex items-center px-5">
                首页
              </a>
            </li>
            <li class="h-full flex items-center justify-center">
              <a href="/services" class="nav-link flex items-center px-5">
                服务
              </a>
            </li>
            <li class="h-full flex items-center justify-center">
              <a href="" class="nav-link flex items-center px-5">
                策略
              </a>
            </li>
            <li class="h-full flex items-center justify-center">
              <a href="" class="nav-link flex items-center px-5">
                案例
              </a>
            </li>
            <li class="h-full flex items-center justify-center">
              <a href="" class="nav-link flex items-center px-5">
                FAQ
              </a>
            </li>
            <li class="h-full flex items-center justify-center">
              <a href="" class="nav-link flex items-center px-5">
                关于我们
              </a>
            </li>
            <li class="h-full flex items-center justify-center">
              <a href="" class="nav-link flex items-center px-5">
                联系我们
              </a>
            </li>
          </ul>
        </nav>

        <!-- 联系信息和二维码 -->
        <div class="hidden lg:flex items-center gap-10">
          <div class="text-white text-sm md:text-base">
            <span class="flex items-center gap-2">
              <i class="iconfont icon-weibiaoti- iconfont-telephone"></i>
              ************
            </span>
          </div>
          <div
            class="w-[3.75rem] h-[3.75rem] border-2 border-[#725e9b] rounded-xl flex items-center justify-center cursor-pointer group relative">
            <i class="iconfont icon-erweima text-white rq-telephone"></i>
            <div
              class="border-2 border-[#725e9b] bg-[#272636] absolute top-[120%] min-w-[11.125rem] p-5 hidden group-hover:block">
              <img src="/img/qr_dow.jpg" class="max-w-full object-cover" alt="">
            </div>
          </div>
        </div>

        <!-- 移动端菜单按钮 -->
        <button id="mobile-menu-btn" class="lg:hidden text-white p-2 cursor-pointer">
          <svg t="1752221010071" class="icon w-6 h-6" viewBox="0 0 1024 1024" version="1.1"
            xmlns="http://www.w3.org/2000/svg" p-id="8317" width="200" height="200">
            <path
              d="M153.6 256h716.8a51.2 51.2 0 0 0 0-102.4H153.6a51.2 51.2 0 0 0 0 102.4zM665.6 460.8H153.6a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4zM460.8 768H153.6a51.2 51.2 0 0 0 0 102.4h307.2a51.2 51.2 0 0 0 0-102.4z"
              fill="#ffffff" p-id="8318"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div id="mobile-menu" class="lg:hidden fixed inset-0 backdrop-blur-xl z-50 hidden h-lvh bg-[#030015]">
      <!-- 菜单头部 -->
      <div class="flex items-center justify-between px-4 border-b border-white/10 h-16">
        <div class="flex items-center space-x-3 ">
          <a href="/">
            <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
          </a>
        </div>
        <button id="mobile-menu-close"
          class="text-white p-2 hover:bg-white/10 rounded-full transition-colors duration-200">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- 菜单内容 -->
      <div class="flex-1 flex flex-col justify-center">
        <nav class="py-8" role="navigation" aria-label="移动端导航">
          <ul class="space-y-0">
            <li>
              <a href="/"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">首页</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/services"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">服务</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/guide"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">策略</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/cases"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">案例</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/faq"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">FAQ</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/about"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">关于我们</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="/contact"
                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                <span class="text-xl font-medium">联系我们</span>
                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
          </ul>
        </nav>
      </div>

      <!-- 菜单底部联系信息 -->
      <div class="p-8 border-t border-white/10">
        <div class="flex items-center justify-center space-x-4 text-white">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
          </svg>
          <span class="text-xl font-medium">************</span>
        </div>
      </div>
    </div>
  </header>
  <!-- 主要内容区域 -->
  <main class="pt-16 lg:pt-[6.25rem]">
    <!-- 获取优化方案 -->
    <section class="md:min-h-screen flex items-center flex-col justify-center px-4 lg:px-8 pb-12 overflow-hidden">
      <div class="container mx-auto text-center">
        <div class="max-w-4xl mx-auto pt-16 md:max-w-max md:pt-[10rem]">
          <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight md:mb-14">
            <span class="geo-text">
              <span class="first-letter">G</span>enerative
            </span>
            <br class="md:hidden">
            <span class="geo-text">
              <span class="first-letter">E</span>ngine
            </span>
            <br class="md:hidden">
            <span class="geo-text">
              <span class="first-letter">O</span>ptimization
            </span>
          </h1>
          <p class="text-lg md:text-3xl lg:text-2xl text-[#afaac6] mb-8 leading-relaxed md:mb-[3.4375rem]">
            GEO让AI生成的内容更“好用”
          </p>
          <div class="flex justify-center w-[12.5rem] md:w-[18.75rem] mx-auto">
            <button
              class="border-2 border-[#afaac6] hover:border-white/50 text-[#f3f3f3] px-4 py-3 rounded-full transition-all duration-300 hover:bg-white/10 text-sm cursor-pointer md:text-xl md:py-3.5 md:w-full">
              获取AI优化方案
            </button>
          </div>
        </div>
      </div>
      <div class="mt-10 wave-container bg-none">
        <picture>
          <source media="(max-width: 600px)" srcset="/img/banner-logo-x-m.png">
          <source media="(min-width: 601px)" srcset="/img/banner-logo-x.png">
          <img src="/img/banner-logo-x.png" class="mx-auto" alt="">
        </picture>
        <!-- 呼吸交互 -->
        <div>
          <div class="wave wave-1"></div>
          <div class="wave wave-2"></div>
          <div class="wave wave-3"></div>
          <div class="wave wave-4"></div>
        </div>
      </div>

    </section>

    <!-- 关于 G E O -->
    <section class="-mt-[3.75rem] lg:px-8">
      <div class="px-4">
        <div
          class="geo-card-container container mx-auto border border-[#725e9b] rounded-xl p-3 bg-black/20 border-b-0 rounded-bl-none rounded-br-none relative">
          <div class="geo-content-area text-center border border-[#343045] border-b-0 rounded-xl pb-20"
            style="background: linear-gradient(360deg, rgba(3,0,21,0) 0%, rgba(3,0,21,1) 100%);">
            <div
              class="flex justify-center w-[9.375rem] mx-auto pt-10 mb-6 md:pt-[4.375rem] md:mb-[1.875rem] md:w-[11.875rem]">
              <span
                class="geo-tag w-full border-2 border-[#412d5a] text-[#efc3ff] text-sm px-4 py-3 rounded-full md:text-base SourceHanSans_Bold  md:h-[3.125rem]">
                关于 G E O
              </span>
            </div>

            <h2 class="text-2xl md:text-4xl lg:text-5xl font-bold mb-6 vertical-gradient-text md:mb-[3.125rem]">
              什么是GEO生成引擎优化
            </h2>
            <p class="text-sm max-w-2xl mx-auto leading-7 text-[#a9a8b9] md:text-xl md:max-w-5xl md:leading-10">
              是指有针对性地创作和优化互联网上的内容，帮助这些内容在用户使用生成式<strong class="text-white">AI应用</strong>时（如ChatGPT或DeepSeek） ，
              <strong class="text-white">获得更好的展示效果和更高的可见性。</strong><br />
              <strong class="text-white">上海源易</strong>在传统SEO领域有超过20年技术经验，也是<strong
                class="text-white">GEO在中国的倡行和领导者。</strong>
            </p>
          </div>
        </div>
      </div>
      <div
        class="bg-[url('/img/banner-wg-m.png')] bg-no-repeat bg-cover bg-center pt-5 pb-20 md:bg-[url('/img/banner-wg.png')] md:pt-[3.75rem] relative z-10 -mt-20 md:h-[42.875rem]">
        <div class="w-[10.875rem] h-[10.875rem] mx-auto md:w-[17.0625rem] md:h-[17.0625rem]">
          <img src="/img/banner-bg-ai.png" alt="" class="w-full object-cover" />
        </div>
      </div>
    </section>
    <!-- GEO优化方向 -->
    <section class="bg-[#f5f5f5] rounded-tl-3xl rounded-tr-3xl md:pb-5">
      <div class="md:w-10/12 mx-auto">
        <!-- 卡片容器 -->
        <div
          class="geo-optimization-card relative overflow-hidden rounded-tl-3xl rounded-tr-3xl bg-[#f5f5f5] py-8 md:pt-[3.75rem]">
          <!-- 标签 -->
          <div class="flex justify-center mb-6 md:mb-[2.125rem]">
            <span
              class="inline-flex items-center px-6 py-2 rounded-full border border-[#725e9b] text-[#725e9b] text-sm font-medium md:h-[3.125rem]  md:w-[11.875rem] md:text-base md:mx-auto md:inline-block text-center md:py-3">
              GEO优化方向
            </span>
          </div>

          <!-- 标题 -->
          <h2
            class="text-2xl md:text-4xl lg:text-5xl font-bold text-center text-[#111111] md:text-[2.5rem] md:mb-[4.375rem]">
            GEO的核心优化方向
          </h2>

          <!-- 优化方向网格 -->
          <div class="relative flex flex-wrap md:grid-cols-2 lg:grid-cols-3 px-2 md:px-0">
            <!-- 渐变分割线 -->
            <!-- 垂直分割线 - 两列布局时的中间分割线 -->
            <div
              class=" md:block lg:hidden absolute top-0 bottom-0 left-1/2 transform -translate-x-1/2 gradient-divider-vertical">
            </div>

            <!-- 垂直分割线 - 三列布局时的分割线 -->
            <div
              class="hidden lg:block absolute top-0 bottom-0 left-1/3 transform -translate-x-1/2 gradient-divider-vertical">
            </div>
            <div
              class="hidden lg:block absolute top-0 bottom-0 left-2/3 transform -translate-x-1/2 gradient-divider-vertical">
            </div>
            <!-- 三列布局时的额外水平分割线 -->
            <div
              class=" lg:block absolute left-0 right-0 top-1/3 transform -translate-y-1/2 gradient-divider-horizontal md:top-1/2">
            </div>
            <div
              class=" lg:hidden absolute left-0 right-0 top-2/3 transform -translate-y-1/2 gradient-divider-horizontal">
            </div>


            <!-- 提升内容质量 -->
            <div class="optimization-item group fade-in-up delay-200 px-2 md:px-[3.125rem]">
              <div class="flex flex-col transition-all duration-30 ">
                <div
                  class="w-8 h-8 mb-4 flex items-center justify-center rounded-full transition-colors duration-300 md:w-[2.5rem] md:h-[2.5rem]">
                  <img src="/img/zhinengyouhua.png" alt="提升内容质量" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4 md:text-xl">提升内容质量</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left md:text-base">
                  <li class="flex items-start md:mb-5">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    增强专业性（如生成符合医学规范的诊断建议）。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 模型调优 -->
            <div class="optimization-item group fade-in-up delay-200 px-4 md:px-[3.125rem]">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center md:w-[2.5rem] md:h-[2.5rem]">
                  <img src="/img/_moxingguanli.png" alt="模型调优" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4 md:text-xl">模型调优</h3>
                <ul class="text-sm text-gray-600 space-y-2 text-left md:text-base">
                  <li class="flex items-start md:mb-5">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    针对特定任务或领域，用垂直数据对预训练模型进行二次训练，提升专业性（例如医疗报告生成）。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    调整模型超参数（如温度值、Top-k采样）以平衡生成内容的创造性和准确性。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 生成控制 -->
            <div class="optimization-item group fade-in-up delay-300 px-4 md:px-[3.125rem]">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center md:w-[2.5rem] md:h-[2.5rem]">
                  <img src="/img/xitongkongzhi.png" alt="生成控制" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4 md:text-xl">生成控制</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left md:text-base">
                  <li class="flex items-start md:mb-5">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    通过条件约束（如关键词、格式模板）控制输出结构。例如，生成广告文案时限定产品卖点和语气。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    结合用户交互反馈（如用户点击、修正）动态调整生成策略。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 后处理优化 -->
            <div class="optimization-item group fade-in-up delay-400 px-4 md:px-[3.125rem]">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center md:w-[2.5rem] md:h-[2.5rem]">
                  <img src="/img/shezhi-5.png" alt="后处理优化" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4 md:text-xl">后处理优化</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left md:text-base">
                  <li class="flex items-start md:mb-5">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    通过规则或小模型对生成内容进行筛选，剔除低质量或不符合要求的结果。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    对生成结果进行二次加工（如调整语气、简化语言）以适配不同场景。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 效率优化 -->
            <div class="optimization-item group fade-in-up delay-500 px-4 md:px-[3.125rem]">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center rounded-full md:w-[2.5rem] md:h-[2.5rem]">
                  <img src="/img/xiaoshuai-2.png" alt="效率优化" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4 md:text-xl">效率优化</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left md:text-base">
                  <li class="flex items-start md:mb-5">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    通过剪枝（Pruning）、量化（Quantization）等技术压缩模型体积，提升推理速度。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    利用GPU/TPU等专用硬件或分布式计算优化生成效率。
                  </li>
                </ul>
              </div>
            </div>

            <!-- 评估与迭代 -->
            <div class="optimization-item group fade-in-up delay-600 px-4 md:px-[3.125rem]">
              <div class="flex flex-col">
                <div class="w-8 h-8 mb-4 flex items-center justify-center rounded-full md:w-[2.5rem] md:h-[2.5rem]">
                  <img src="/img/pinggumoban.png" alt="评估与迭代" class="w-full h-full">
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-4 md:text-xl">评估与迭代</h3>
                <ul class="text-xs text-gray-600 space-y-2 text-left md:text-base">
                  <li class="flex items-start md:mb-5">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    基础指标（BLEU、ROUGE）结合业务指标（转化率）。
                  </li>
                  <li class="flex items-start">
                    <span class="w-2 h-2 bg-[#7f29ff] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    标注错误生成结果反哺模型迭代。
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- GEO生成引擎优化领航者 -->
    <section
      class="geo-leader-section lg:px-8 overflow-hidden bg-[url('/img/bj-m.jpg')] bg-no-repeat bg-bottom bg-cover h-[9.375rem] px-4 md:h-[18.75rem] md:bg-[url('/img/book_bg.jpg')] md:bg-cover">
      <!-- 内容容器 -->
      <div class="container mx-auto relative z-10 h-full">
        <div class="flex items-center justify-center gap-4 h-full md:items-center md:gap-x-[4.375rem]">
          <!-- 左侧书籍图片 -->
          <div
            class="flex-shrink-0 w-28 flex justify-center lg:justify-start md:w-[21.75rem] md:h-full md:items-end md:-mb-[6.25rem]">
            <img src="/img/book_pc.png" alt="GEO优化书籍" class="w-full">
          </div>

          <!-- 右侧内容 -->
          <div class="flex-1 text-center lg:text-left md:flex-none">
            <!-- 主标题 -->
            <h2 class="text-xl md:text-5xl font-bold mb-4 vertical-gradient-text md:text-[2.5rem] md:mb-[2.5rem]">
              "GEO" 生成引擎优化领航者
            </h2>

            <!-- CTA按钮 -->
            <div class="flex justify-center lg:justify-start md:pl-[2.5rem]">
              <button
                class="geo-cta-button group relative overflow-hidden bg-white text-[#111111] font-bold text-lg rounded-md hover:bg-gray-100 transition-all duration-300 hover:shadow-purple-500/25 md:w-[25rem] md:h-[3.75rem] md:text-xl">
                <a href="" class="block relative z-10">获取AI优化方案</a>
                <div
                  class="absolute inset-0 border-2 border-transparent bg-gradient-to-r from-purple-500 to-blue-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300">
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="px-4 bg-[#030015] pb-[3.25rem] md:px-0">
      <div
        class="flex justify-center w-[9.375rem] mx-auto pt-10 mb-6 md:pt-[4.375rem] md:mb-[1.875rem] md:w-[11.875rem]">
        <span
          class="geo-tag w-full border-2 border-[#412d5a] text-[#efc3ff] text-sm px-4 py-3 rounded-full md:text-base SourceHanSans_Bold  md:h-[3.125rem] text-center">
          GEO和SEO对比
        </span>
      </div>
      <h2
        class="text-2xl md:text-4xl lg:text-5xl font-bold text-center vertical-gradient-text mb-6 md:text-[2.5rem] md:mb-[5.625rem]">
        GEO与SEO的本质区别
      </h2>
      <div class="w-full">
        <picture>
          <source media="(max-width: 600px)" srcset="/img/comparison.png">
          <source media="(min-width: 601px)" srcset="/img/comparison_pc.png">
          <img src="/img/comparison_pc.png" class="mx-auto" alt="">
        </picture>

      </div>
    </section>

    <!-- GEO的价值和必要性 -->
    <section
      class="geo-value-section py-12 px-4 lg:px-0 bg-[#ffffff] rounded-tl-3xl rounded-tr-3xl md:rounded-tl-4xl md:rounded-tr-4xl">
      <div class="container mx-auto">
        <!-- 标签按钮 -->
        <div class="flex justify-center mb-6 md:mb-[2.125rem]">
          <span
            class="inline-flex items-center px-6 py-2 rounded-full border border-[#725e9b] text-[#725e9b] text-sm font-medium md:h-[3.125rem]  md:w-[11.875rem] md:text-base md:mx-auto md:inline-block text-center md:py-3">
            GEO价值
          </span>
        </div>

        <!-- 主标题 -->
        <h2 class="text-2xl md:text-4xl font-bold text-center text-gray-900 mb-4 md:text-[2.5rem] md:mb-[1.875rem]">
          GEO的价值和必要性
        </h2>

        <!-- 描述文字 -->
        <div class="text-center text-[#999] mb-12 space-y-4 leading-7 text-sm md:text-base md:mb-[3.75rem]">
          <p>GEO的价值和必要性源于生成式人工智能（AIGC）技术的快速发展和广泛应用<br
              class="hidden md:block">随着文本、图像、音频、视频等生成模型的普及，如何让这些引擎更精准、高效、可控地服务于实际需求，成为亟待解决的核心问题</p>
        </div>

        <!-- 价值卡片 -->
        <div class="space-y-5 md:grid md:grid-cols-2 md:gap-5 md:space-y-0">
          <div class="geo-value-card bg-[#f5f5f5] rounded-2xl p-6 border border-[#ccc] md:py-[1.875rem] md:px-[2.5rem]">
            <div class="flex items-start gap-2 md:items-baseline">
              <div class="flex-shrink-0">
                <span class="comparison-text text-3xl font-bold md:text-[4.375rem]">1</span>
              </div>
              <div class="flex-1 pt-2">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提升生成内容的质量与可靠性</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">减少低质量输出：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        通过优化提示词、微调模型或后处理过滤，避免生成内容中的逻辑错误、事实性偏差或低相关性结果。
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">增强专业性：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        在垂直领域（如法律、医疗、金融）优化生成引擎，确保内容符合行业 规范和准确性要求（例如生成合规的医疗报告）。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="geo-value-card bg-[#f5f5f5] rounded-2xl p-6 border border-[#ccc]">
            <div class="flex items-start gap-4 md:items-baseline"">
              <div class="flex-shrink-0">
                <span class="comparison-text text-3xl font-bold md:text-[4.375rem]">2</span>
              </div>
              <div class="flex-1 pt-2">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提高生成效率与成本效益</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">降低计算成本：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        通过模型压缩、量化或硬件加速技术，减少生成所需的算力资源。例如， 轻量化模型可部署在边缘设备（如手机）上实时生成内容。
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">加速迭代速度：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        优化后的生成引擎能快速响应用户需求（如广告文案的批量生成），提升 业务效率。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="geo-value-card bg-[#f5f5f5] rounded-2xl p-6 border border-[#ccc]">
            <div class="flex items-start gap-4 md:items-baseline"">
              <div class="flex-shrink-0">
                <span class="comparison-text text-3xl font-bold md:text-[4.375rem]">3</span>
              </div>
              <div class="flex-1 pt-2">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提高生成效率与成本效益</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">降低计算成本：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        通过模型压缩、量化或硬件加速技术，减少生成所需的算力资源。例如， 轻量化模型可部署在边缘设备（如手机）上实时生成内容。
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">加速迭代速度：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        优化后的生成引擎能快速响应用户需求（如广告文案的批量生成），提升 业务效率。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="geo-value-card bg-[#f5f5f5] rounded-2xl p-6 border border-[#ccc]">
            <div class="flex items-start gap-4 md:items-baseline"">
              <div class="flex-shrink-0">
                <span class="comparison-text text-3xl font-bold md:text-[4.375rem]">4</span>
              </div>
              <div class="flex-1 pt-2">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提高生成效率与成本效益</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">降低计算成本：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        通过模型压缩、量化或硬件加速技术，减少生成所需的算力资源。例如， 轻量化模型可部署在边缘设备（如手机）上实时生成内容。
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">加速迭代速度：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        优化后的生成引擎能快速响应用户需求（如广告文案的批量生成），提升 业务效率。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="geo-value-card bg-[#f5f5f5] rounded-2xl p-6 border border-[#ccc]">
            <div class="flex items-start gap-4 md:items-baseline"">
              <div class="flex-shrink-0">
                <span class="comparison-text text-3xl font-bold md:text-[4.375rem]">5</span>
              </div>
              <div class="flex-1 pt-2">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提高生成效率与成本效益</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">降低计算成本：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        通过模型压缩、量化或硬件加速技术，减少生成所需的算力资源。例如， 轻量化模型可部署在边缘设备（如手机）上实时生成内容。
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">加速迭代速度：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        优化后的生成引擎能快速响应用户需求（如广告文案的批量生成），提升 业务效率。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="geo-value-card bg-[#f5f5f5] rounded-2xl p-6 border border-[#ccc]">
            <div class="flex items-start gap-4 md:items-baseline"">
              <div class="flex-shrink-0">
                <span class="comparison-text text-3xl font-bold md:text-[4.375rem]">6</span>
              </div>
              <div class="flex-1 pt-2">
                <h3 class="text-xl font-bold text-gray-900 mb-4">提高生成效率与成本效益</h3>
                <div class="space-y-3">
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">降低计算成本：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        通过模型压缩、量化或硬件加速技术，减少生成所需的算力资源。例如， 轻量化模型可部署在边缘设备（如手机）上实时生成内容。
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start gap-2">
                    <div class="w-5 h-5 rounded-full flex-shrink-0 md:w-[1.5625rem] md:h-[1.5625rem]">
                      <svg t="1752485708907" class="icon w-full h-full" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="4490">
                        <path
                          d="M510.8 129.2c-211.9 0-383.7 171.8-383.7 383.7 0 211.9 171.8 383.7 383.7 383.7 211.9 0 383.7-171.8 383.7-383.7 0-211.9-171.8-383.7-383.7-383.7z m153.3 250.2S494.8 674.2 491.9 676.9c-9.7 9.1-24.8 8.6-33.9-1.1l-109.6-101c-9.1-9.7-8.6-24.8 1.1-33.9 9.7-9.1 24.8-8.6 33.9 1.1l85.8 79.1 153.4-265.7c6.6-11.5 21.3-15.4 32.8-8.8 11.4 6.7 15.3 21.4 8.7 32.8z"
                          fill="#0c0101" p-id="4491"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-gray-900 mb-1 md:text-[1.125rem]">加速迭代速度：</h4>
                      <p class="text-gray-600 text-sm md:text-base">
                        优化后的生成引擎能快速响应用户需求（如广告文案的批量生成），提升 业务效率。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>

    </section>

    <section class="geo-services-section pb-16 px-4 lg:px-8  bg-[url(/img/bg-2.png)] bg-[right_80%] bg-no-repeat text-white bg-size-[35.25rem] md:pb-[11.25rem]">
      <div class="container mx-auto md:w-10/12">
        <!-- 标题和描述 -->
        <div class="flex justify-center w-[9.375rem] mx-auto pt-10 mb-6 md:pt-[6.25rem] md:mb-[1.875rem] md:w-[11.875rem]">
        <span class="geo-tag w-full border-2 border-[#412d5a] text-[#efc3ff] text-sm px-4 py-3 rounded-full md:text-base SourceHanSans_Bold  md:h-[3.125rem] text-center">
          GEO服务
        </span>
      </div>
        <h2 class="text-2xl md:text-4xl lg:text-5xl font-bold text-center vertical-gradient-text mb-6">
          GEO服务
        </h2>
        <div class="text-center text-[#999] mb-12 space-y-4 leading-7 text-sm px-3">
          <p>通过技术、数据和流程的调整，提升生成式AI模型在质量、效率、可控性等方面的表现</p>
        </div>

        <!-- 服务选项卡 -->
        <div class="mb-8">
          <div thumbsSlider="" class="swiper mySwiper">
            <div class="swiper-wrapper">
              <div class="swiper-slide">
                <div
                  class="service-tab active flex items-center justify-center gap-3 px-6 py-4 rounded-xl border border-purple-500 bg-purple-600 text-white cursor-pointer transition-all duration-300"
                  data-index="0">
                  <img src="/img/icon_1.png" class="w-6" alt="">
                  <span class="font-medium">Deepseek GEO服务</span>
                </div>
              </div>
              <div class="swiper-slide">
                <div
                  class="service-tab flex items-center justify-center gap-3 px-6 py-4 rounded-xl border border-gray-600 bg-gray-700 text-gray-300 cursor-pointer transition-all duration-300 hover:border-purple-500 hover:bg-purple-600 hover:text-white"
                  data-index="1">
                  <img src="/img/icon_2.png" class="w-6" alt="">
                  <span class="font-medium">文小言 GEO服务</span>
                </div>
              </div>
              <div class="swiper-slide">
                <div
                  class="service-tab flex items-center justify-center gap-3 px-6 py-4 rounded-xl border border-gray-600 bg-gray-700 text-gray-300 cursor-pointer transition-all duration-300 hover:border-purple-500 hover:bg-purple-600 hover:text-white"
                  data-index="2">
                  <img src="/img/icon_3.png" class="w-6" alt="">
                  <span class="font-medium">KIMI GEO服务</span>
                </div>
              </div>
              <div class="swiper-slide">
                <div
                  class="service-tab flex items-center justify-center gap-3 px-6 py-4 rounded-xl border border-gray-600 bg-gray-700 text-gray-300 cursor-pointer transition-all duration-300 hover:border-purple-500 hover:bg-purple-600 hover:text-white"
                  data-index="3">
                  <img src="/img/icon_4.png" class="w-6" alt="">
                  <span class="font-medium">豆包 GEO服务</span>
                </div>
              </div>
              <div class="swiper-slide">
                <div
                  class="service-tab flex items-center justify-center gap-3 px-6 py-4 rounded-xl border border-gray-600 bg-gray-700 text-gray-300 cursor-pointer transition-all duration-300 hover:border-purple-500 hover:bg-purple-600 hover:text-white"
                  data-index="4">
                  <img src="/img/icon_5.png" class="w-8" alt="">
                  <span class="font-medium">并行宇宙 GEO服务</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="swiper mySwiper1 text-white">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <div class="service-content rounded-2xl p-8 border border-gray-700 geo-tag bg-[url(/img/swiper-bg.png)] bg-no-repeat bg-[right_bottom]">
                <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-700 md:pb-[1.875rem] md:mb-[1.875rem]">
                  <div class="flex items-center gap-3">
                    <img src="/img/icon_6.png" class="w-4" alt="">
                    <h3 class="text-lg vertical-gradient-text line-clamp-1 md:text-xl">什么是Deep seek GEO服务</h3>
                  </div>
                  <a href="" class="text-gray-400 hover:text-white transition-colors">
                    <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                  </a>
                </div>
                <div class="space-y-4 text-[#8a8f99] leading-relaxed text-xs md:text-base md:min-h-[31.25rem]">
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。
                  </p>
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板
                  </p>
                </div>
              </div>
              <div class="mt-6 md:w-[25rem] md:h-[4.375rem] md:mx-auto md:mt-[2.9375rem]">
                <a href=""
                  class=" flex items-center justify-around text-center px-8 py-3.5 w-full h-full md:text-xl bg-transparent border border-[#725e9b] hover:bg-gray-600 text-[#eec3ff] rounded-xl transition-colors duration-300 font-medium geo-tag">
                  了解更多
                </a>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="service-content rounded-2xl p-8 border border-gray-700 geo-tag bg-[url(/img/swiper-bg.png)] bg-no-repeat bg-[right_bottom]">
                <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-700 md:pb-[1.875rem] md:mb-[1.875rem]">
                  <div class="flex items-center gap-3">
                    <img src="/img/icon_6.png" class="w-4" alt="">
                    <h3 class="text-lg vertical-gradient-text line-clamp-1 md:text-xl">什么是Deep seek GEO服务</h3>
                  </div>
                  <a href="" class="text-gray-400 hover:text-white transition-colors">
                    <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                  </a>
                </div>
                <div class="space-y-4 text-[#8a8f99] leading-relaxed text-xs md:text-base md:min-h-[31.25rem]">
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。
                  </p>
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板
                  </p>
                </div>
              </div>
              <div class="mt-6 md:w-[25rem] md:h-[4.375rem] md:mx-auto md:mt-[2.9375rem]">
                <a href=""
            class=" flex items-center justify-around text-center px-8 py-3.5 w-full h-full md:text-xl bg-transparent border border-[#725e9b] hover:bg-gray-600 text-[#eec3ff] rounded-xl transition-colors duration-300 font-medium geo-tag">
            了解更多
          </a>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="service-content rounded-2xl p-8 border border-gray-700 geo-tag bg-[url(/img/swiper-bg.png)] bg-no-repeat bg-[right_bottom]">
                <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-700 md:pb-[1.875rem] md:mb-[1.875rem]">
                  <div class="flex items-center gap-3">
                    <img src="/img/icon_6.png" class="w-4" alt="">
                    <h3 class="text-lg vertical-gradient-text line-clamp-1 md:text-xl">什么是Deep seek GEO服务</h3>
                  </div>
                  <a href="" class="text-gray-400 hover:text-white transition-colors">
                    <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                  </a>
                </div>
                <div class="space-y-4 text-[#8a8f99] leading-relaxed text-xs md:text-base md:min-h-[31.25rem]">
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。
                  </p>
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板
                  </p>
                </div>
              </div>
              <div class="mt-6 md:w-[25rem] md:h-[4.375rem] md:mx-auto md:mt-[2.9375rem]">
                <a href=""
            class=" flex items-center justify-around text-center w-full h-full md:text-xl bg-transparent border border-[#725e9b] hover:bg-gray-600 text-[#eec3ff] rounded-xl transition-colors duration-300 font-medium geo-tag">
            了解更多
          </a>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="service-content rounded-2xl p-8 border border-gray-700 geo-tag bg-[url(/img/swiper-bg.png)] bg-no-repeat bg-[right_bottom]">
                <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-700 md:pb-[1.875rem] md:mb-[1.875rem]">
                  <div class="flex items-center gap-3">
                    <img src="/img/icon_6.png" class="w-4" alt="">
                    <h3 class="text-lg vertical-gradient-text line-clamp-1 md:text-xl">什么是Deep seek GEO服务</h3>
                  </div>
                  <a href="" class="text-gray-400 hover:text-white transition-colors">
                    <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                  </a>
                </div>
                <div class="space-y-4 text-[#8a8f99] leading-relaxed text-xs md:text-base md:min-h-[31.25rem]">
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。
                  </p>
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板
                  </p>
                </div>
              </div>
              <div class="mt-6 md:w-[25rem] md:h-[4.375rem] md:mx-auto md:mt-[2.9375rem]">
                <a href=""
            class=" flex items-center justify-around text-center px-8 py-3.5 w-full h-full md:text-xl bg-transparent border border-[#725e9b] hover:bg-gray-600 text-[#eec3ff] rounded-xl transition-colors duration-300 font-medium geo-tag">
            了解更多
          </a>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="service-content rounded-2xl p-8 border border-gray-700 geo-tag bg-[url(/img/swiper-bg.png)] bg-no-repeat bg-[right_bottom]">
                <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-700 md:pb-[1.875rem] md:mb-[1.875rem]">
                  <div class="flex items-center gap-3">
                    <img src="/img/icon_6.png" class="w-4" alt="">
                    <h3 class="text-lg vertical-gradient-text line-clamp-1 md:text-xl">什么是Deep seek GEO服务</h3>
                  </div>
                  <a href="" class="text-gray-400 hover:text-white transition-colors">
                    <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                  </a>
                </div>
                <div class="space-y-4 text-[#8a8f99] leading-relaxed text-xs md:text-base md:min-h-[31.25rem]">
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。
                  </p>
                  <p>
                    结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板（如“以学术风格总结以下内容，不超过300字”）明确生成目标。结构化提示：通过指令模板
                  </p>
                </div>
              </div>
              <div class="mt-6 md:w-[25rem] md:h-[4.375rem] md:mx-auto md:mt-[2.9375rem]">
                  <a href=""
                    class=" flex items-center justify-around text-center px-8 py-3.5 w-full h-full md:text-xl bg-transparent border border-[#725e9b] hover:bg-gray-600 text-[#eec3ff] rounded-xl transition-colors duration-300 font-medium geo-tag">
                    了解更多
                  </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="geo-services-section py-10 px-4 lg:px-8 text-white relative md:pb-[6.875rem]"
      style="background: linear-gradient(360deg, #030015 0%, #030015 25%, #0e0027 50%, #40007f 75%, #5400a1 100%)">
      <!-- 左侧渐变黑色遮罩 -->
      <div
        class="absolute top-0 left-0 w-1/3 h-full bg-gradient-to-r from-black to-transparent opacity-70 pointer-events-none">
      </div>
      <!-- 右侧渐变黑色遮罩 -->
      <div
        class="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-black to-transparent opacity-70 pointer-events-none">
      </div>

      <div class="container mx-auto md:w-10/12">
        <!-- 标题和描述 -->
        <div class="flex justify-center w-[9.375rem] mx-auto mb-6 md:mb-[1.875rem] md:w-[11.875rem]">
          <span
            class="geo-tag w-full border-2 border-[#725e9b] text-[#efc3ff] text-sm px-4 py-3 rounded-full md:text-base SourceHanSans_Bold md:h-[3.125rem] text-center">
            GEO案例
          </span>
        </div>
        <h2 class="text-2xl md:text-4xl lg:text-5xl font-bold text-center vertical-gradient-text mb-6 md:text-[2.5rem]">
          GEO案例展示
        </h2>
        <div class="text-center text-[#8a8f99] mb-12 space-y-4 leading-7 text-xs px-3 relative z-10 md:text-base md:mb-[2.5rem]">
          <p>不同行业中 GEO的实际应用案例，涵盖文本、图像、音频等多模态生成场景，展示如何通过GEO策略解决具体问题并创造价值</p>
        </div>

        <!-- 案例展示网格 -->
        <div class="grid grid-cols-1 gap-5 mb-8 relative z-10 md:grid-cols-3">
          <!-- 案例卡片 1 -->
          <div
            class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
            <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
              <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
            </div>
            <div class="py-6 md:pb-0">
              <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                某跨境电商平台的AI文案生成
              </h3>
              <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
              </p>
              <div class="flex items-center justify-end gap-x-4 md:mt-10">
                <span class="text-white text-sm font-medium">查看更多</span>
                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
              </div>
            </div>
            <a href="" class="absolute left-0 top-0 w-full h-full"></a>
          </div>

          <!-- 案例卡片 2 -->
          <div
            class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
            <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
              <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
            </div>
            <div class="py-6 md:pb-0">
              <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                某跨境电商平台的AI文案生成
              </h3>
              <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
              </p>
              <div class="flex items-center justify-end gap-x-4 md:mt-10">
                <span class="text-white text-sm font-medium">查看更多</span>
                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
              </div>
            </div>
            <a href="" class="absolute left-0 top-0 w-full h-full"></a>
          </div>

          <div
            class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
            <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
              <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
            </div>
            <div class="py-6 md:pb-0">
              <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                某跨境电商平台的AI文案生成
              </h3>
              <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
              </p>
              <div class="flex items-center justify-end gap-x-4 md:mt-10">
                <span class="text-white text-sm font-medium">查看更多</span>
                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
              </div>
            </div>
            <a href="" class="absolute left-0 top-0 w-full h-full"></a>
          </div>

          <div
            class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
            <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
              <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
            </div>
            <div class="py-6 md:pb-0">
              <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                某跨境电商平台的AI文案生成
              </h3>
              <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
              </p>
              <div class="flex items-center justify-end gap-x-4 md:mt-10">
                <span class="text-white text-sm font-medium">查看更多</span>
                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
              </div>
            </div>
            <a href="" class="absolute left-0 top-0 w-full h-full"></a>
          </div>
          <div
            class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
            <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
              <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
            </div>
            <div class="py-6 md:pb-0">
              <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                某跨境电商平台的AI文案生成
              </h3>
              <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
              </p>
              <div class="flex items-center justify-end gap-x-4 md:mt-10">
                <span class="text-white text-sm font-medium">查看更多</span>
                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
              </div>
            </div>
            <a href="" class="absolute left-0 top-0 w-full h-full"></a>
          </div>
          <div
            class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
            <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
              <img src="/img/case_1.jpg" class="object-cover w-full" alt="" />
            </div>
            <div class="py-6 md:pb-0">
              <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                某跨境电商平台的AI文案生成
              </h3>
              <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
              </p>
              <div class="flex items-center justify-end gap-x-4 md:mt-10">
                <span class="text-white text-sm font-medium">查看更多</span>
                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
              </div>
            </div>
            <a href="" class="absolute left-0 top-0 w-full h-full"></a>
          </div>
        </div>

        <!-- 了解更多按钮 -->
        <div class="mt-6 md:w-[25rem] md:h-[4.375rem] md:mx-auto md:mt-[5.125rem]">
          <a href=""
            class=" flex items-center justify-around text-center px-8 py-3.5 w-full h-full md:text-xl bg-transparent border border-[#725e9b] hover:bg-gray-600 text-[#eec3ff] rounded-xl transition-colors duration-300 font-medium geo-tag">
            了解更多
          </a>
        </div>
      </div>
    </section>

    <!-- GEO问答 -->
    <section class="geo-news-section py-16 px-4 lg:px-8 bg-white rounded-tl-4xl rounded-tr-4xl md:pb-[6.25rem]">
      <div class="container mx-auto md:w-10/12">
        <!-- 标题和描述 -->
        <div class="text-center mb-12">
          <!-- GEO问答 -->
          <div class="flex justify-center mb-6 md:mb-[2.125rem]">
          <span class="inline-flex items-center px-6 py-2 rounded-full border border-[#725e9b] text-[#725e9b] text-sm font-medium md:h-[3.125rem]  md:w-[11.875rem] md:text-base md:mx-auto md:inline-block text-center md:py-3">
            GEO问答
          </span>
        </div>

          <!-- 主标题 -->
          <h2 class="text-2xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            FAQ
          </h2>

          <!-- 描述文字 -->
          <p class="text-[#8a8f99] text-xs leading-relaxed max-w-2xl mx-auto md:text-base">
            最新的技术、技术和应用趋势的权威文章和资源推荐
          </p>
        </div>

        <!-- 新闻列表 -->
        <div class="mb-8 grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-y-2 md:gap-x-10">
          <!-- 新闻项目 1 -->
          <a href=""
            class="news-item bg-white py-3 border-b border-[#cccccc] group block md:py-5">
            <div class="flex items-start gap-4 ">
              <!-- 新闻图片 -->
              <div class="flex-shrink-0 w-28 h-full overflow-hidden md:w-[11.25rem] md:h-[8.125rem]">
                <img src="/img/news.jpg" alt="GEO新闻图片"
                  class="w-full h-full object-cover group-hover:scale-101 transition-transform duration-300">
              </div>

              <!-- 新闻内容 -->
              <div class="flex-1">
                <h3
                  class="text-sm mb-2 font-bold text-[#111111] group-hover:text-purple-600 transition-colors line-clamp-2 md:text-xl">
                  从"关键词堆砌"到"实体信任"：揭秘GEO的核心优化逻辑
                </h3>
                <p class="text-[#8a8f99] text-xs leading-relaxed line-clamp-2 md:text-base">
                  在AI技术高度发展的数字化今天，用户不再满足于传统搜索引擎的链接列表而希望直接获得ChatGPT、DeepSeek等生成式AI答案
                </p>
              </div>
            </div>
          </a>

          <!-- 新闻项目 2 -->
          <a href=""
            class="news-item bg-white py-3 border-b border-[#cccccc] group block md:py-5">
            <div class="flex items-start gap-4 ">
              <!-- 新闻图片 -->
              <div class="flex-shrink-0 w-28 h-full overflow-hidden md:w-[11.25rem] md:h-[8.125rem]">
                <img src="/img/news.jpg" alt="GEO新闻图片"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
              </div>

              <!-- 新闻内容 -->
              <div class="flex-1">
                <h3
                  class="text-sm mb-2 font-bold text-[#111111] group-hover:text-purple-600 transition-colors line-clamp-2 md:text-xl">
                  从"关键词堆砌"到"实体信任"：揭秘GEO的核心优化逻辑
                </h3>
                <p class="text-[#8a8f99] text-xs leading-relaxed line-clamp-2 md:text-base">
                  在AI技术高度发展的数字化今天，用户不再满足于传统搜索引擎的链接列表而希望直接获得ChatGPT、DeepSeek等生成式AI答案
                </p>
              </div>
            </div>
          </a>

          <!-- 新闻项目 3 -->
          <a href=""
            class="news-item bg-white py-3 border-b border-[#cccccc] group block md:py-5">
            <div class="flex items-start gap-4 ">
              <!-- 新闻图片 -->
              <div class="flex-shrink-0 w-28 h-full overflow-hidden md:w-[11.25rem] md:h-[8.125rem]">
                <img src="/img/news.jpg" alt="GEO新闻图片"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
              </div>

              <!-- 新闻内容 -->
              <div class="flex-1">
                <h3
                  class="text-sm mb-2 font-bold text-[#111111] group-hover:text-purple-600 transition-colors line-clamp-2 md:text-xl">
                  从"关键词堆砌"到"实体信任"：揭秘GEO的核心优化逻辑
                </h3>
                <p class="text-[#8a8f99] text-xs leading-relaxed line-clamp-2 md:text-base">
                  在AI技术高度发展的数字化今天，用户不再满足于传统搜索引擎的链接列表而希望直接获得ChatGPT、DeepSeek等生成式AI答案
                </p>
              </div>
            </div>
          </a>

          <!-- 新闻项目 4 -->
          <a href=""
            class="news-item bg-white py-3 border-b border-[#cccccc] group block md:py-5">
            <div class="flex items-start gap-4 ">
              <!-- 新闻图片 -->
              <div class="flex-shrink-0 w-28 h-full overflow-hidden md:w-[11.25rem] md:h-[8.125rem]">
                <img src="/img/news.jpg" alt="GEO新闻图片"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
              </div>

              <!-- 新闻内容 -->
              <div class="flex-1">
                <h3
                  class="text-sm mb-2 font-bold text-[#111111] group-hover:text-purple-600 transition-colors line-clamp-2 md:text-xl">
                  从"关键词堆砌"到"实体信任"：揭秘GEO的核心优化逻辑
                </h3>
                <p class="text-[#8a8f99] text-xs leading-relaxed line-clamp-2 md:text-base">
                  在AI技术高度发展的数字化今天，用户不再满足于传统搜索引擎的链接列表而希望直接获得ChatGPT、DeepSeek等生成式AI答案
                </p>
              </div>
            </div>
          </a>
          <a href=""
            class="news-item bg-white py-3 border-b border-[#cccccc] group block md:py-5">
            <div class="flex items-start gap-4 ">
              <!-- 新闻图片 -->
              <div class="flex-shrink-0 w-28 h-full overflow-hidden md:w-[11.25rem] md:h-[8.125rem]">
                <img src="/img/news.jpg" alt="GEO新闻图片"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
              </div>

              <!-- 新闻内容 -->
              <div class="flex-1">
                <h3
                  class="text-sm mb-2 font-bold text-[#111111] group-hover:text-purple-600 transition-colors line-clamp-2 md:text-xl">
                  从"关键词堆砌"到"实体信任"：揭秘GEO的核心优化逻辑
                </h3>
                <p class="text-[#8a8f99] text-xs leading-relaxed line-clamp-2 md:text-base">
                  在AI技术高度发展的数字化今天，用户不再满足于传统搜索引擎的链接列表而希望直接获得ChatGPT、DeepSeek等生成式AI答案
                </p>
              </div>
            </div>
          </a>
          <a href=""
            class="news-item bg-white py-3 border-b border-[#cccccc] group block md:py-5">
            <div class="flex items-start gap-4 ">
              <!-- 新闻图片 -->
              <div class="flex-shrink-0 w-28 h-full overflow-hidden md:w-[11.25rem] md:h-[8.125rem]">
                <img src="/img/news.jpg" alt="GEO新闻图片"
                  class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
              </div>

              <!-- 新闻内容 -->
              <div class="flex-1">
                <h3
                  class="text-sm mb-2 font-bold text-[#111111] group-hover:text-purple-600 transition-colors line-clamp-2 md:text-xl">
                  从"关键词堆砌"到"实体信任"：揭秘GEO的核心优化逻辑
                </h3>
                <p class="text-[#8a8f99] text-xs leading-relaxed line-clamp-2 md:text-base">
                  在AI技术高度发展的数字化今天，用户不再满足于传统搜索引擎的链接列表而希望直接获得ChatGPT、DeepSeek等生成式AI答案
                </p>
              </div>
            </div>
          </a>
        </div>

        <!-- 了解更多按钮 -->
        <div class="mt-6 md:w-[25rem] md:h-[4.375rem] md:mx-auto md:mt-[5.125rem]">
          <a href=""
            class="flex items-center justify-center px-8 py-3.5  bg-white w-full h-full border border-[#412d5a] text-[#111111] font-medium rounded-xl hover:border-purple-400 hover:text-purple-600 hover:bg-purple-50 transition-all duration-300 SourceHanSans_Bold md:text-xl">
            <span>了解更多</span>
          </a>
        </div>
      </div>
    </section>
    <section
      class="h-[18.75rem] bg-[url(/img/ai_bj_m.jpg)] bg-no-repeat bg-cover bg-center relative flex items-end overflow-hidden md:h-[34.375rem] md:bg-[url(/img/ai_bj.png)] md:bg-cover">
      <div class="container mx-auto px-4 md:w-[18.75rem] md:h-[3.75rem] md:pb-[7.5rem]">
        <a href=""
          class="inline-flex items-center justify-center px-8 py-3 border-2 border-[#dfc5ff] text-[#fff] font-medium rounded-xl hover:border-purple-400 hover:text-purple-600 hover:bg-purple-50 transition-all duration-300 w-full SourceHanSans_Bold mb-8">
          立即获取AI优化方案
        </a>
      </div>

    </section>

  </main>
  <footer class="py-10 bg-[url(/img/footer_bg.png)] bg-no-repeat bg-[center_bottom] flex flex-col items-center gap-y-8 md:pt-20 bg-[#030015] border-t border-[#343045]">
    <a href="">
      <img src="/img/footer_logo.png" alt="" />
    </a>
    <p class="uppercase text-[#bfc4ce] text-sm">
      © 2025 yuanyi, Inc. All rights reserved
    </p>
  </footer>
  <script src="/js/vendors/jquery-1.8.3.min.js"></script>
  <script src="/js/vendors/swiper-bundle.min.js"></script>
  <script src="/js/main.js"></script>
  <script>
    var swiper = new Swiper(".mySwiper", {
      spaceBetween: 10,
      slidesPerView: 1.5,
      // freeMode: true,
      watchSlidesProgress: true,
      breakpoints: {
        640: {
          slidesPerView: 2,
          spaceBetween: 20,
        },
        768: {
          slidesPerView: 4,
          spaceBetween: 40,
        },
        1024: {
          slidesPerView: 6,
          spaceBetween: 20,
        },
      },
    });
    var swiper2 = new Swiper(".mySwiper1", {
      spaceBetween: 10,
      autoHeight: true,
      thumbs: {
        swiper: swiper,
      },
      on: {
        slideChange: function () {
          updateActiveTab(this.activeIndex);
        }
      }
    });

    // 更新激活状态的函数
    function updateActiveTab(activeIndex) {
      // 移除所有按钮的激活状态
      document.querySelectorAll('.service-tab').forEach(tab => {
        tab.classList.remove('active');
        tab.classList.remove('border-purple-500', 'bg-purple-600', 'text-white');
        tab.classList.add('border-gray-600', 'bg-gray-700', 'text-gray-300');
      });

      // 为当前激活的按钮添加激活状态
      const activeTab = document.querySelector(`.service-tab[data-index="${activeIndex}"]`);
      if (activeTab) {
        activeTab.classList.add('active');
        activeTab.classList.remove('border-gray-600', 'bg-gray-700', 'text-gray-300');
        activeTab.classList.add('border-purple-500', 'bg-purple-600', 'text-white');
      }
    }

    // 为按钮添加点击事件
    document.querySelectorAll('.service-tab').forEach((tab, index) => {
      tab.addEventListener('click', function () {
        swiper2.slideTo(index);
        updateActiveTab(index);
      });
    });

    // 初始化时设置第一个按钮为激活状态
    document.addEventListener('DOMContentLoaded', function () {
      updateActiveTab(0);
    });
  </script>


</body>

</html>