<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <!-- 高DPI显示优化 -->
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>服务详情 - PARALLEL UNIVERSE</title>
    <link rel="shortcut icon" href="/favicon.ico">
    </link>
    <!-- 字体图标 -->
    <link rel="stylesheet" href="/css/iconfont.css" />
    <link href="/css/style.css" rel="stylesheet">
</head>

<body class="min-h-screen">
    <!-- 主导航栏 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-[#030015]  border-b border-[#3e3e3e]" id="navbar">
        <div class="md:max-w-10/12 mx-auto px-4 lg:px-0">
            <div class="flex items-center justify-between h-16 lg:h-[6.25rem]">
                <!-- Logo -->
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>

                <!-- 桌面导航菜单 -->
                <nav class="hidden lg:block h-full lg:flex-1 lg:ml-12" role="navigation" aria-label="主导航">
                    <ul class="flex items-center h-full space-x-8">
                        <li class="h-full flex items-center justify-center">
                            <a href="/" class="nav-link flex items-center px-5">
                                首页
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/services" class="nav-link flex items-center px-5 active">
                                服务
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                策略
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                案例
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                FAQ
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                关于我们
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                联系我们
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- 联系信息和二维码 -->
                <div class="hidden lg:flex items-center gap-10">
                    <div class="text-white text-sm md:text-base">
                        <span class="flex items-center gap-2">
                            <i class="iconfont icon-weibiaoti- iconfont-telephone"></i>
                            ************
                        </span>
                    </div>
                    <div
                        class="w-[3.75rem] h-[3.75rem] border-2 border-[#725e9b] rounded-xl flex items-center justify-center cursor-pointer group relative">
                        <i class="iconfont icon-erweima text-white rq-telephone"></i>
                        <div
                            class="border-2 border-[#725e9b] bg-[#272636] absolute top-[120%] min-w-[11.125rem] p-5 hidden group-hover:block">
                            <img src="/img/qr_dow.jpg" class="max-w-full object-cover" alt="">
                        </div>
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="lg:hidden text-white p-2 cursor-pointer">
                    <svg t="1752221010071" class="icon w-6 h-6" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="8317" width="200" height="200">
                        <path
                            d="M153.6 256h716.8a51.2 51.2 0 0 0 0-102.4H153.6a51.2 51.2 0 0 0 0 102.4zM665.6 460.8H153.6a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4zM460.8 768H153.6a51.2 51.2 0 0 0 0 102.4h307.2a51.2 51.2 0 0 0 0-102.4z"
                            fill="#ffffff" p-id="8318"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="lg:hidden fixed inset-0 backdrop-blur-xl z-50 hidden h-lvh bg-[#030015]">
            <!-- 菜单头部 -->
            <div class="flex items-center justify-between px-4 border-b border-white/10 h-16">
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>
                <button id="mobile-menu-close"
                    class="text-white p-2 hover:bg-white/10 rounded-full transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- 菜单内容 -->
            <div class="flex-1 flex flex-col justify-center">
                <nav class="py-8" role="navigation" aria-label="移动端导航">
                    <ul class="space-y-0">
                        <li>
                            <a href="/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">首页</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/services"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">服务</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/guide"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">策略</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/cases"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">案例</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/faq"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">FAQ</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/about"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">关于我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/contact"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">联系我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 菜单底部联系信息 -->
            <div class="p-8 border-t border-white/10">
                <div class="flex items-center justify-center space-x-4 text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
                    </svg>
                    <span class="text-xl font-medium">************</span>
                </div>
            </div>
        </div>
    </header>

    <main class="pt-16 md:pt-[6.25rem] min-h-screen bg-[#0d0b1e] text-white md:pb-10">

        <!-- 面包屑导航 -->
        <nav class="flex items-center space-x-2 text-[#bc8bff] py-6 text-base w-11/12 mx-auto md:w-10/12 border-b border-[#3e3e3e] mb-8"
            aria-label="面包屑导航">
            <a href="/" class="hover:text-white transition-colors duration-200">首页</a>
            <span>></span>
            <a href="/cases" class="hover:text-white transition-colors duration-200">案例</a>
            <span>></span>
            <span class="text-white max-w-3xs line-clamp-1">DeepSeek</span>
        </nav>
        <div class="mb-12 w-11/12 mx-auto md:w-10/12">
            <h1 class="text-3xl md:text-5xl font-bold text-white mb-6 leading-tight">
                DeepSeek
            </h1>
            <!-- 案例详细内容 -->
            <div class="editor-content text-base leading-8 mb-20">
                <p>
                    <img src="/img/case_2.jpg" alt="">
                </p>
                <p>
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。

                </p>
                <p>
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。

                </p>
                <p>
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。
                </p>

            </div>
        </div>
        <div class="py-16 bg-[#1f1c2e]">
            <div class="w-11/12 md:w-10/12 mx-auto">
                <!-- 主标题 -->
                <div class="mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold">GEO策略</h2>
                </div>

                <!-- 策略步骤 -->
                <div class="space-y-10">

                    <div class="border-b border-[#3e3e3e] pb-10 flex flex-col md:flex-row md:gap-x-10">
                        <div class="flex items-center mb-6 flex-shrink-0">
                            <h3 class="text-xl border border-[#3e3e3e] bg-[#292638] rounded-full py-2.5 px-6">提示词选择
                            </h3>
                        </div>
                        <div class="leading-relaxed text-base text-[#cccccc]">
                            <p>
                                设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。
                            </p>
                        </div>
                    </div>

                    <div class="border-b border-[#3e3e3e] pb-10 flex flex-col md:flex-row md:gap-x-10">
                        <div class="flex items-center mb-6 flex-shrink-0">
                            <h3 class="text-xl border border-[#3e3e3e] bg-[#292638] rounded-full py-2.5 px-6">内容生成
                            </h3>
                        </div>
                        <div class="leading-relaxed text-base text-[#cccccc]">
                            <p>
                                设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。
                            </p>
                        </div>
                    </div>

                    <div class="border-b border-[#3e3e3e] pb-10 flex flex-col md:flex-row md:gap-x-10">
                        <div class="flex items-start mb-6 flex-shrink-0">
                            <h3 class="text-xl border border-[#3e3e3e] bg-[#292638] rounded-full py-2.5 px-6">发布媒体
                            </h3>
                        </div>
                        <div class="leading-relaxed text-base">
                            <p>
                                <img src="/img/GEO_02.jpg" alt="">
                            </p>
                        </div>
                    </div>

                    <div class="pb-10 flex flex-col md:flex-row md:gap-x-10">
                        <div class="flex items-start mb-6 flex-shrink-0">
                            <h3 class="text-xl border border-[#3e3e3e] bg-[#292638] rounded-full py-2.5 px-6">监控及报告
                            </h3>
                        </div>
                        <div class="leading-relaxed text-base text-[#cccccc]">
                            <p>
                                设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如"生成3条科技风广告文案，突出绿新和轻薄，字数≤20字"）。动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。
                            </p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <section class="mt-16 bg-[#0d0b1e] md:pt-10">
            <div class="w-11/12 mx-auto md:w-10/12">
                <div class="mb-8 md:mb-10">
                    <h2 class="text-2xl font-bold text-white md:text-4xl">服务的优势</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 md:gap-5">

                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <i class="iconfont icon-zhinengyouhua text-4xl vertical-gradient-text"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">精准生成</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    减少错误、矛盾或低相关性输出（如避免AI"胡言乱语"）。
                                </p>
                            </div>
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    增强专业性（如生成符合医学规范的诊断建议）。
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <i class="iconfont icon-sanweimoxing text-4xl vertical-gradient-text"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">多模态协同</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    针对特定任务或领域，用垂直数据对预训练模型进行二次训练，提升专业性（例如医疗报告生成）。
                                </p>
                            </div>
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    调整模型超参数（如温度值、Top-k采样）以平衡生成内容的创造性和准确性。
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <i class="iconfont icon-xitongkongzhi text-4xl vertical-gradient-text"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">极致可控</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    通过条件约束（如关键词、格式模板）控制输出结构。例如，生成广告文案时限定产品卖点和语气。
                                </p>
                            </div>
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    结合用户交互反馈（如用户点击、修正）动态调整生成策略。
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <i class="iconfont icon-shezhi text-3xl vertical-gradient-text"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">安全与合规</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    通过规则或小模型对生成内容进行筛选，剔除低质量或不符合要求的结果。
                                </p>
                            </div>
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    对生成结果进行二次加工（如调整语气、简化语言）以适配不同场景。
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <i class="iconfont icon-xiaoshuai text-4xl vertical-gradient-text"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">效率突破</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    通过剪枝（Pruning）、量化（Quantization）等技术压缩模型体积，提升推理速度。
                                </p>
                            </div>
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    利用GPU/TPU等专用硬件或分布式计算优化生成效率。
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <i class="iconfont icon-pinggumoban text-3xl vertical-gradient-text"></i>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">持续进化</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    基础指标（BLEU、ROUGE）结合业务指标（转化率）。
                                </p>
                            </div>
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 bg-custom-gradient rounded-full mt-2 flex-shrink-0"></div>
                                <p class="text-[#8a8f99] leading-relaxed">
                                    标注错误生成结果反哺模型迭代。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mb-8 md:mb-10">
                    <h2 class="text-2xl font-bold text-white md:text-4xl">合作流程</h2>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 md:pb-24 md:mb-0">

                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="vertical-gradient-text italic block text-3xl font-bold md:text-[4.375rem]">1</span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">精准生成</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <p class="text-[#8a8f99] leading-relaxed">
                                    减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="vertical-gradient-text italic block text-3xl font-bold md:text-[4.375rem]">2</span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">精准生成</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <p class="text-[#8a8f99] leading-relaxed">
                                    减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="vertical-gradient-text italic block text-3xl font-bold md:text-[4.375rem]">3</span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold text-[#d0d6df] mb-6">精准生成</h3>

                        <!-- 优势列表 -->
                        <div class="space-y-4">
                            <div class="flex items-start gap-3">
                                <p class="text-[#8a8f99] leading-relaxed">
                                    减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。减少错误、矛盾或低相关性输出（如避免AI“胡言乱语”）。
                                </p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>
        <!-- 相关案例推荐 -->
        <section class="bg-[#1f1c2e]">
            <div class=" w-11/12 mx-auto md:w-10/12 pt-14">
                <div class="flex items-center justify-between mb-8">
                    <h2 class="text-2xl font-bold text-white md:text-4xl">推荐案例</h2>
                    <a href=""
                        class="border border-[#725e9b] flex items-center gap-10 justify-around px-4 py-2 rounded-lg inner-glow">
                        <span>了解更多</span>
                        <i class="iconfont icon-fangxiang-jiantouyoushang text-2xl md:text-2xl"></i>
                    </a>
                </div>
                <div class="grid grid-cols-1 gap-5 relative z-10 md:grid-cols-3">
                    <!-- 案例卡片 1 -->
                    <div
                        class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                        <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                            <img src="/img/case_1.jpg" class="object-cover w-full" alt="">
                        </div>
                        <div class="py-6 md:pb-0">
                            <h3
                                class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                                某跨境电商平台的AI文案生成
                            </h3>
                            <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                                人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                            </p>
                            <div class="flex items-center justify-end gap-x-4 md:mt-10">
                                <span class="text-white text-sm font-medium">查看更多</span>
                                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                            </div>
                        </div>
                        <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                    </div>

                    <!-- 案例卡片 2 -->
                    <div
                        class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                        <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                            <img src="/img/case_1.jpg" class="object-cover w-full" alt="">
                        </div>
                        <div class="py-6 md:pb-0">
                            <h3
                                class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                                某跨境电商平台的AI文案生成
                            </h3>
                            <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                                人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                            </p>
                            <div class="flex items-center justify-end gap-x-4 md:mt-10">
                                <span class="text-white text-sm font-medium">查看更多</span>
                                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                            </div>
                        </div>
                        <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                    </div>

                    <div
                        class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                        <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                            <img src="/img/case_1.jpg" class="object-cover w-full" alt="">
                        </div>
                        <div class="py-6 md:pb-0">
                            <h3
                                class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                                某跨境电商平台的AI文案生成
                            </h3>
                            <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                                人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                            </p>
                            <div class="flex items-center justify-end gap-x-4 md:mt-10">
                                <span class="text-white text-sm font-medium">查看更多</span>
                                <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                            </div>
                        </div>
                        <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <footer
        class="py-10 bg-[url(/img/footer_bg.png)] bg-no-repeat bg-[center_bottom] flex flex-col items-center gap-y-8 md:pt-20 bg-[#030015] border-t border-[#343045]">
        <a href="">
            <i class="iconfont icon-binghangyuzhou2 text-4xl md:text-5xl vertical-gradient-text"></i>
        </a>
        <p class="uppercase text-[#bfc4ce] text-sm">
            © 2025 yuanyi, Inc. All rights reserved
        </p>
    </footer>
    <script src="/js/vendors/jquery-1.8.3.min.js"></script>
    <script src="/js/main.js"></script>

</body>

</html>