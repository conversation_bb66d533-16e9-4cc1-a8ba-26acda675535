@import "tailwindcss";

/* 自定义基础样式 */
@layer base {
  html {
    scroll-behavior: smooth;
    font-size: 16px;
  }

  body {
    font-family: 'SourceHanSans_Regular', ui-sans-serif, system-ui, sans-serif;
  }
}

@font-face {
  font-family: "SourceHanSans_Bold";
  src: url(/fonts/SourceHanSans-Bold.woff2);
}

@font-face {
  font-family: "SourceHanSans_Regular";
  src: url(/fonts/SourceHanSans-Regular.woff2);
}

/* 自定义组件样式 */
@layer components {
  /* 导航样式 */
  .nav-link {
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
    font-weight: 400;
    letter-spacing: 0.025em;
  }

  .nav-link:hover {
    color: #c084fc;
    transform: translateY(-1px);
  }

  /* 导航指示器 */
  .nav-link .nav-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #6c00ff;
    transition: all 0.3s ease;
    transform: translateX(-50%);
    border-radius: 1px;
  }

  /* 当前页面样式 */
  .nav-link.active {
    color: #ffffff;
    font-weight: 500;
  }

  .nav-link.active .nav-indicator {
    width: 100%;
    box-shadow: 0 0 8px rgba(168, 85, 247, 0.4);
  }

  /* 悬浮效果 */
  .nav-link:hover .nav-indicator {
    width: 100%;
    background: #6c00ff;
  }

  /* 当前页面悬浮时保持原色 */
  .nav-link.active:hover .nav-indicator {
    background: #6c00ff;
  }

  /* 导航列表样式重置 */
  nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  nav li {
    margin: 0;
    padding: 0;
  }

  /* 移动端导航样式 */
  .mobile-nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-decoration: none;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
  }

  .mobile-nav-link:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
    padding-left: 3rem;
  }

  /* 移动端导航列表 */
  #mobile-menu nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  #mobile-menu nav li {
    margin: 0;
    padding: 0;
  }

  /* 导航容器增强 */
  nav[role="navigation"] {
    height: 100%;
  }

  /* 响应式导航调整 */
  @media (max-width: 1024px) {
    .nav-link {
      font-size: 0.9rem;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }
  }

  .wave-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        /* background: #000;  */
        background: url('/img/banner-logo-x-m.png') no-repeat center 90%;
        background-size: 100%;
      }

      .wave {
        position: absolute;
        top: 100%;
        z-index: -1;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100vmax; /* 确保波纹覆盖整个屏幕 */
        height: 100vmax;
        border-radius: 50%;
        opacity: 0;
        background: radial-gradient(
          circle at center,
          var(--color),
          transparent 70%
        );
        animation: 
          breath 4s infinite ease-in-out,
          ripple 4s infinite ease-out;
      }

      /* 分别设置4种颜色 */
      .wave-1 {
        --color: rgba(238, 119, 82, 0.7);
        animation-delay: 0s;
      }
      .wave-2 {
        --color: rgba(231, 60, 126, 0.7);
        animation-delay: 1s;
      }
      .wave-3 {
        --color: rgba(35, 166, 213, 0.7);
        animation-delay: 2s;
      }
      .wave-4 {
        --color: rgba(35, 213, 171, 0.7);
        animation-delay: 3s;
      }

      /* 呼吸效果（透明度变化） */
      @keyframes breath {
        0%, 100% {
          opacity: 0;
        }
        50% {
          opacity: 0.8;
        }
      }

      /* 波纹扩散效果 */
      @keyframes ripple {
        0% {
          transform: translate(-50%, -50%) scale(0);
        }
        100% {
          transform: translate(-50%, -50%) scale(1);
        }
      }

  /* GEO标签样式 */
  .geo-tag {
    /* 内阴影效果 */
    box-shadow:
      inset 0px 0px .4375rem .1875rem rgba(110, 90, 152, .5);
  }

  .geo-tag:hover {
    /* 悬浮时的内阴影增强 */
    box-shadow:
      inset 0 3px 6px rgba(110, 90, 152, 0),
      inset 0 3px 6px rgba(110, 90, 152, 1);
      

    /* 悬浮时的背景变化 */
    background: linear-gradient(135deg,
      rgba(75, 55, 110, 0.8) 0%,
      rgba(55, 40, 80, 1.0) 100%);

    /* 增强文字阴影 */
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
  }

  /* GEO优化方向卡片 */
  .geo-optimization-card {
    background: linear-gradient(135deg,
      rgba(249, 250, 251, 0.95) 0%,
      rgba(255, 255, 255, 0.98) 50%,
      rgba(243, 244, 246, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.8),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  /* 优化项目样式 */
  .optimization-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    @apply flex-1/2 h-[22.5rem] pt-10 md:flex-1/3
  }

  .optimization-item:hover {
    transform: translateY(-5px);
  }

  .optimization-item .group:hover {
    box-shadow:
      0 20px 40px -12px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(147, 51, 234, 0.1);
  }

  /* 渐变分割线 */
  .gradient-divider-vertical {
    background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(204, 204, 204, 0.5) 5%,
      rgba(204, 204, 204, 0.8) 30%,
      rgba(204, 204, 204, 1) 50%,
      rgba(204, 204, 204, 0.8) 70%,
      rgba(204, 204, 204, 0.5) 90%,
      transparent 100%
    );
    z-index: 1;
    width: 2px;
  }

  .gradient-divider-horizontal {
    background: linear-gradient(
      to right,
      transparent 0%,
      rgba(204, 204, 204, 0.5) 15%,
      rgba(204, 204, 204, 1) 50%,
      rgba(204, 204, 204, 0.8) 70%,
      transparent 100%
    );
    z-index: 1;
    height: 2px;
  }

  /* 分割线动画效果 */
  .gradient-divider-vertical,
  .gradient-divider-horizontal {
    animation: divider-glow 3s ease-in-out infinite alternate;
  }

  @keyframes divider-glow {
    from {
      opacity: 0.6;
      filter: brightness(1);
    }
    to {
      opacity: 1;
      filter: brightness(1.2);
    }
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .geo-optimization-card {
      padding: 1.5rem;
    }
  }

  @media (max-width: 480px) {
    .geo-optimization-card {
      padding: 1rem;
    }
  }

  /* 纵向渐变文字 */
  .vertical-gradient-text {
    /* 使用您提供的渐变色 */
    background: rgb(255,255,255);
    background: linear-gradient(0deg, rgba(255,255,255,0.9416141456582633) 0%, rgba(234,176,255,1) 50%, rgba(56,156,255,1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    text-shadow: none;

    /* 添加发光效果 */
    filter: drop-shadow(0 0 10px rgba(234, 176, 255, 0.3));

    /* 确保文字清晰度 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

    .vertical-gradient-text {
    /* 使用您提供的渐变色 */
    background: rgb(255,255,255);
    background: linear-gradient(0deg, rgba(255,255,255,0.9416141456582633) 0%, rgba(234,176,255,1) 50%, rgba(56,156,255,1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    text-shadow: none;

    /* 添加发光效果 */
    filter: drop-shadow(0 0 10px rgba(234, 176, 255, 0.3));

    /* 确保文字清晰度 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

    .comparison-text{
      font-style: italic;
      background: rgb(127,41,255);
      background: linear-gradient(0deg, rgba(127,41,255,1) 0%, rgba(45,94,255,1) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      text-shadow: none;

      /* 添加发光效果 */
      filter: drop-shadow(0 0 10px rgba(234, 176, 255, 0.3));

      /* 确保文字清晰度 */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

  /* 浏览器兼容性处理 */
  @supports not ((background-clip: text) or (-webkit-background-clip: text)) {
    .vertical-gradient-text,
    .comparison-text {
      /* 降级方案：使用纯白色 */
      color: #ffffff !important;
      background: none !important;
    }
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .vertical-gradient-text {
      filter: drop-shadow(0 0 8px rgba(234, 176, 255, 0.4));
    }
  }

  @media (max-width: 480px) {
    .vertical-gradient-text {
      filter: drop-shadow(0 0 6px rgba(234, 176, 255, 0.5));
    }
  }



  /* GEO文字效果 */
  .geo-text {
    display: inline-block;
    font-weight: 900;
    letter-spacing: 0.05em;
    color: transparent;
    -webkit-text-stroke: 1px #9893af;
    position: relative;
  }

  .geo-text .first-letter {
    color: #9893af;
    -webkit-text-stroke: 0;
    position: relative;
    z-index: 1;
    background: linear-gradient(180deg, #ffffff 0%, #f5f0ff 70%, #43149a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .geo-text {
      -webkit-text-stroke: 1.5px #9893ae;
      letter-spacing: 0.03em;
    }
  }

  @media (max-width: 480px) {
    .geo-text {
      -webkit-text-stroke: 1px #9893ae;
      letter-spacing: 0.02em;
    }
  }
}

/* 自定义工具类 */
@layer utilities {
  /* 防止背景滚动 */
  .overflow-hidden {
    overflow: hidden;
  }

  /* 渐入动画 */
  .fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 延迟动画 */
  .delay-100 { animation-delay: 0.1s; }
  .delay-200 { animation-delay: 0.2s; }
  .delay-300 { animation-delay: 0.3s; }
  .delay-400 { animation-delay: 0.4s; }
  .delay-500 { animation-delay: 0.5s; }
  .delay-600 { animation-delay: 0.6s; }

  /* 悬浮发光效果 */
  .hover-glow:hover {
    box-shadow:
      0 20px 40px -12px rgba(147, 51, 234, 0.2),
      0 0 0 1px rgba(147, 51, 234, 0.1);
    transition: box-shadow 0.3s ease;
  }


  .geo-cta-button {
    transform: translateY(0);
    transition: all 0.3s ease;
  }

  .geo-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(147, 51, 234, 0.2);
  }

  /* GEO价值部分样式 */
  .geo-value-section {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }

  .geo-value-card {
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
  }

  .geo-value-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #c084fc;
  }

  /* 编号圆圈样式 */
  .geo-value-card .w-8 {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .geo-value-card {
      padding: 1.5rem;
    }

    .geo-value-card .flex-shrink-0 {
      align-self: flex-start;
    }
  }

}
