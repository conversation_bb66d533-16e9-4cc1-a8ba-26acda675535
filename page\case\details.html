<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <!-- 高DPI显示优化 -->
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title>案例 - PARALLEL UNIVERSE</title>
    <link rel="shortcut icon" href="/favicon.ico">
    </link>
    <!-- 字体图标 -->
    <link rel="stylesheet" href="/css/iconfont.css" />
    <link href="/css/style.css" rel="stylesheet">
</head>

<body class="min-h-screen">
    <!-- 主导航栏 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-[#030015]  border-b border-[#3e3e3e]" id="navbar">
        <div class="md:max-w-10/12 mx-auto px-4 lg:px-0">
            <div class="flex items-center justify-between h-16 lg:h-[6.25rem]">
                <!-- Logo -->
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>

                <!-- 桌面导航菜单 -->
                <nav class="hidden lg:block h-full lg:flex-1 lg:ml-12" role="navigation" aria-label="主导航">
                    <ul class="flex items-center h-full space-x-8">
                        <li class="h-full flex items-center justify-center">
                            <a href="/" class="nav-link flex items-center px-5">
                                首页
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/services" class="nav-link flex items-center px-5">
                                服务
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                策略
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5 active">
                                案例
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                FAQ
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                关于我们
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                联系我们
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- 联系信息和二维码 -->
                <div class="hidden lg:flex items-center gap-10">
                    <div class="text-white text-sm md:text-base">
                        <span class="flex items-center gap-2">
                            <i class="iconfont icon-weibiaoti- iconfont-telephone"></i>
                            ************
                        </span>
                    </div>
                    <div
                        class="w-[3.75rem] h-[3.75rem] border-2 border-[#725e9b] rounded-xl flex items-center justify-center cursor-pointer group relative">
                        <i class="iconfont icon-erweima text-white rq-telephone"></i>
                        <div
                            class="border-2 border-[#725e9b] bg-[#272636] absolute top-[120%] min-w-[11.125rem] p-5 hidden group-hover:block">
                            <img src="/img/qr_dow.jpg" class="max-w-full object-cover" alt="">
                        </div>
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="lg:hidden text-white p-2 cursor-pointer">
                    <svg t="1752221010071" class="icon w-6 h-6" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="8317" width="200" height="200">
                        <path
                            d="M153.6 256h716.8a51.2 51.2 0 0 0 0-102.4H153.6a51.2 51.2 0 0 0 0 102.4zM665.6 460.8H153.6a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4zM460.8 768H153.6a51.2 51.2 0 0 0 0 102.4h307.2a51.2 51.2 0 0 0 0-102.4z"
                            fill="#ffffff" p-id="8318"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="lg:hidden fixed inset-0 backdrop-blur-xl z-50 hidden h-lvh bg-[#030015]">
            <!-- 菜单头部 -->
            <div class="flex items-center justify-between px-4 border-b border-white/10 h-16">
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>
                <button id="mobile-menu-close"
                    class="text-white p-2 hover:bg-white/10 rounded-full transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- 菜单内容 -->
            <div class="flex-1 flex flex-col justify-center">
                <nav class="py-8" role="navigation" aria-label="移动端导航">
                    <ul class="space-y-0">
                        <li>
                            <a href="/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">首页</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/services"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">服务</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/guide"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">策略</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/cases"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">案例</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/faq"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">FAQ</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/about"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">关于我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/contact"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">联系我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 菜单底部联系信息 -->
            <div class="p-8 border-t border-white/10">
                <div class="flex items-center justify-center space-x-4 text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
                    </svg>
                    <span class="text-xl font-medium">************</span>
                </div>
            </div>
        </div>
    </header>

    <main class="pt-16 md:pt-[6.25rem] min-h-screen bg-[#0d0b1e] text-white md:pb-10">
        
            <!-- 面包屑导航 -->
            <nav class="flex items-center space-x-2 text-[#bc8bff] py-6 text-base w-11/12 mx-auto md:w-10/12 border-b border-[#3e3e3e] mb-8"
                aria-label="面包屑导航">
                <a href="/" class="hover:text-white transition-colors duration-200">首页</a>
                <span>></span>
                <a href="/cases" class="hover:text-white transition-colors duration-200">案例</a>
                <span>></span>
                <span class="text-white max-w-3xs line-clamp-1">某跨境电商平台的AI文案生成某跨境电商平台的AI文案生成</span>
            </nav>
        <div class="mb-12 w-11/12 mx-auto md:w-10/12">
            <h1 class="text-3xl md:text-5xl font-bold text-white mb-6 leading-tight">
                某跨境电商平台的AI文案生成
            </h1>

            <!-- 案例详情头部 -->
            <div class="flex items-center gap-6 text-base text-[#8a8f99] mb-8">
                <div class="flex items-center gap-2">
                    <span>2025年06月17日</span>
                </div>
                <div class="flex items-center gap-2">
                    <span>编辑：</span>
                    <span class="text-white">作者名</span>
                </div>
            </div>
            <!-- 案例详细内容 -->
            <div class="editor-content text-base leading-8 mb-12">
                <p>
                    <img src="/img/case_2.jpg" alt="">
                </p>
                <p>
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。

                </p>
                <p>
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。

                </p>
                <p>
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。
                    人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。人工撰写多语言商品描述成本高，且通用生成模型（如GPT-3）生成的文案缺乏针对性，转化率低。
                </p>

            </div>
            <div class="flex flex-col md:flex-row md:items-center justify-between border-t border-[#3e3e3e] pt-8">
                <div class="flex items-center gap-x-2">
                    <span class="-rotate-180 block"><i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i></span>
                    <a href="" class="text-[#ddd] hover:text-[#007bff] max-w-xs line-clamp-1">生成的文案缺乏针对性，转化率低缺乏针对性，转化率低</a>
                </div>
                <div class="flex items-center gap-x-2">
                    
                    <a href="" class="text-[#ddd] hover:text-[#007bff] max-w-xs line-clamp-1">生成的文案缺乏针对性，转化率低缺乏针对性，转化率低</a>
                    <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                </div>
            </div>
        </div>
        <!-- 相关案例推荐 -->
        <section class="mt-16 bg-[#1f1c2e]">
            <div class=" w-11/12 mx-auto md:w-10/12 pt-14"> 
                <div class="flex items-center justify-between mb-8">
                    <h2 class="text-2xl font-bold text-white md:text-4xl">推荐案例</h2>
                    <a href="" class="border border-[#725e9b] flex items-center gap-10 justify-around px-4 py-2 rounded-lg inner-glow">
                        <span>了解更多</span>
                        <i class="iconfont icon-fangxiang-jiantouyoushang text-2xl md:text-2xl"></i>
                    </a>
                </div>
                <div class="grid grid-cols-1 gap-5 relative z-10 md:grid-cols-3">
                <!-- 案例卡片 1 -->
                <div class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="">
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>

                <!-- 案例卡片 2 -->
                <div class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="">
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>

                <div class="case-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-[#343045] rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-300 group p-4 relative hover:bg-[url(/img/swiper-bg.png)] hover:bg-no-repeat hover:bg-cover">
                    <div class="aspect-video relative overflow-hidden flex items-center justify-center rounded-xl">
                        <img src="/img/case_1.jpg" class="object-cover w-full" alt="">
                    </div>
                    <div class="py-6 md:pb-0">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors line-clamp-1">
                            某跨境电商平台的AI文案生成
                        </h3>
                        <p class="text-sm leading-relaxed mb-4 text-[#8a8f99] line-clamp-2">
                            人工撰写多语言商品描述成本大、自适应性差的问题（"GPT-3"）生成的文案转换率针对性、转化率低。
                        </p>
                        <div class="flex items-center justify-end gap-x-4 md:mt-10">
                            <span class="text-white text-sm font-medium">查看更多</span>
                            <i class="iconfont icon-fangxiang-jiantouyou text-2xl md:text-2xl"></i>
                        </div>
                    </div>
                    <a href="" class="absolute left-0 top-0 w-full h-full"></a>
                </div>
            </div>
            </div>
        </section>
    </main>
    <footer
        class="py-10 bg-[url(/img/footer_bg.png)] bg-no-repeat bg-[center_bottom] flex flex-col items-center gap-y-8 md:pt-20 bg-[#030015] border-t border-[#343045]">
        <a href="">
            <img src="/img/footer_logo.png" alt="" />
        </a>
        <p class="uppercase text-[#bfc4ce] text-sm">
            © 2025 yuanyi, Inc. All rights reserved
        </p>
    </footer>
    <script src="/js/vendors/jquery-1.8.3.min.js"></script>
    <script src="/js/main.js"></script>

</body>

</html>