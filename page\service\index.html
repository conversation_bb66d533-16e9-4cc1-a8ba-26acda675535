<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <!-- 高DPI显示优化 -->
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>策略 - PARALLEL UNIVERSE</title>
    <link rel="shortcut icon" href="/favicon.ico">
    </link>
    <!-- 字体图标 -->
    <link rel="stylesheet" href="/css/iconfont.css" />
    <link href="/css/style.css" rel="stylesheet">
</head>

<body class="min-h-screen">
    <!-- 主导航栏 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-[#030015]  border-b border-[#3e3e3e]" id="navbar">
        <div class="md:max-w-10/12 mx-auto px-4 lg:px-0">
            <div class="flex items-center justify-between h-16 lg:h-[6.25rem]">
                <!-- Logo -->
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>

                <!-- 桌面导航菜单 -->
                <nav class="hidden lg:block h-full lg:flex-1 lg:ml-12" role="navigation" aria-label="主导航">
                    <ul class="flex items-center h-full space-x-8">
                        <li class="h-full flex items-center justify-center">
                            <a href="/" class="nav-link flex items-center px-5">
                                首页
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/services/" class="nav-link flex items-center px-5 active">
                                服务
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/page/strategy/" class="nav-link flex items-center px-5">
                                策略
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/page/case/" class="nav-link flex items-center px-5">
                                案例
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/page/faq/" class="nav-link flex items-center px-5">
                                FAQ
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                关于我们
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                联系我们
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- 联系信息和二维码 -->
                <div class="hidden lg:flex items-center gap-10">
                    <div class="text-white text-sm md:text-base">
                        <span class="flex items-center gap-2">
                            <i class="iconfont icon-weibiaoti- iconfont-telephone"></i>
                            ************
                        </span>
                    </div>
                    <div
                        class="w-[3.75rem] h-[3.75rem] border-2 border-[#725e9b] rounded-xl flex items-center justify-center cursor-pointer group relative">
                        <i class="iconfont icon-erweima text-white rq-telephone"></i>
                        <div
                            class="border-2 border-[#725e9b] bg-[#272636] absolute top-[120%] min-w-[11.125rem] p-5 hidden group-hover:block">
                            <img src="/img/qr_dow.jpg" class="max-w-full object-cover" alt="">
                        </div>
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="lg:hidden text-white p-2 cursor-pointer">
                    <svg t="1752221010071" class="icon w-6 h-6" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="8317" width="200" height="200">
                        <path
                            d="M153.6 256h716.8a51.2 51.2 0 0 0 0-102.4H153.6a51.2 51.2 0 0 0 0 102.4zM665.6 460.8H153.6a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4zM460.8 768H153.6a51.2 51.2 0 0 0 0 102.4h307.2a51.2 51.2 0 0 0 0-102.4z"
                            fill="#ffffff" p-id="8318"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="lg:hidden fixed inset-0 backdrop-blur-xl z-50 hidden h-lvh bg-[#030015]">
            <!-- 菜单头部 -->
            <div class="flex items-center justify-between px-4 border-b border-white/10 h-16">
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>
                <button id="mobile-menu-close"
                    class="text-white p-2 hover:bg-white/10 rounded-full transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- 菜单内容 -->
            <div class="flex-1 flex flex-col justify-center">
                <nav class="py-8" role="navigation" aria-label="移动端导航">
                    <ul class="space-y-0">
                        <li>
                            <a href="/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">首页</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/page/service/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">服务</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/page/strategy/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">策略</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/page/case/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">案例</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href=/page/faq"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">FAQ</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/page/about"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">关于我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/contact"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">联系我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 菜单底部联系信息 -->
            <div class="p-8 border-t border-white/10">
                <div class="flex items-center justify-center space-x-4 text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
                    </svg>
                    <span class="text-xl font-medium">************</span>
                </div>
            </div>
        </div>
    </header>

    <main class="pt-16 md:pt-[6.25rem] min-h-screen bg-[#0d0b1e] text-white md:pb-10">
        <div class="relative mb-10 md:mb-16 text-center">
            <picture>
                <source media="(max-width: 600px)" srcset="/img/fqa_bg_m.jpg">
                <source media="(min-width: 601px)" srcset="/img/fqa_bg.jpg">
                <img src="/img/fqa_bg.jpg" alt="" class="w-full object-cover">
            </picture>
            <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <h1 class="text-5xl font-bold text-center mb-3.5 SourceHanSans_Regular md:text-8xl md:mb-8">
                    服务
                </h1>
                <p class="text-lg">
                    服务简介！注意字数
                </p>
            </div>
        </div>

        <div class="w-11/12 md:w-10/12 mx-auto text-white">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 pb-16 md:gap-5">

                <div
                    class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300 bg-[url(/img/Recommendation_01.png)] bg-no-repeat bg-size-[100%] flex flex-col gap-10">
                    <div class="flex-1">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="flex w-[4rem] h-[4rem] bg-[#343045] rounded-2xl items-center justify-center">
                                <i class="iconfont icon-deepseek2 text-2xl vertical-gradient-text"></i>
                            </span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold mb-4">DeepSeek</h3>

                        <!-- 优势列表 -->
                        <div class="flex flex-col">
                            <p class="leading-relaxed">
                                设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。
                                动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。
                                动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。

                            </p>
                        </div>
                    </div>
                    <a href="" class="flex items-center gap-2">
                        <span class="">查看更多</span>
                        <i class="iconfont icon-fangxiang-jiantouyou text-2xl vertical-gradient-text"></i>
                    </a>
                </div>

                <div
                    class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300 bg-[url(/img/Recommendation_02.png)] bg-no-repeat bg-size-[100%] flex flex-col gap-10">
                    <div class="flex-1">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="flex w-[4rem] h-[4rem] bg-[#343045] rounded-2xl items-center justify-center">
                                <i class="iconfont icon-doubaodamoxing text-4xl vertical-gradient-text"></i>
                            </span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold mb-6">多模态协同</h3>

                        <!-- 优势列表 -->
                        <div class="flex flex-col">
                            <p class="leading-relaxed">
                                设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。
                                动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。
                                动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。

                            </p>
                        </div>
                    </div>
                    <a href="" class="flex items-center gap-2">
                        <span class="">查看更多</span>
                        <i class="iconfont icon-fangxiang-jiantouyou text-2xl vertical-gradient-text"></i>
                    </a>
                </div>

                <div
                    class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300 bg-[url(/img/Recommendation_03.png)] bg-no-repeat bg-size-[100%] flex flex-col gap-10">
                    <div class="flex-1">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="flex w-[4rem] h-[4rem] bg-[#343045] rounded-2xl items-center justify-center">
                                <i class="iconfont icon-baiduwxyy text-4xl vertical-gradient-text"></i>
                            </span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold mb-6">极致可控</h3>

                        <!-- 优势列表 -->
                        <div class="flex flex-col">
                            <p class="leading-relaxed">
                                设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。
                                动态提示：根据用户画像实时调整输入（如针对青少年使用网络流行语）。

                            </p>
                        </div>
                    </div>
                    <a href="" class="flex items-center gap-2">
                        <span class="">查看更多</span>
                        <i class="iconfont icon-fangxiang-jiantouyou text-2xl vertical-gradient-text"></i>
                    </a>
                </div>

                <div
                    class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300 bg-[url(/img/Recommendation_04.png)] bg-no-repeat bg-size-[100%] flex flex-col gap-10">
                    <div class="flex-1">

                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="flex w-[4rem] h-[4rem] bg-[#343045] rounded-2xl items-center justify-center">
                                <i class="iconfont icon-kimi-copy text-4xl vertical-gradient-text"></i>
                            </span>
                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold mb-6">安全与合规</h3>

                        <!-- 优势列表 -->
                        <div class="flex flex-col">
                            <p class="leading-relaxed">
                                设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。

                            </p>
                        </div>
                    </div>
                    <a href="" class="flex items-center gap-2">
                        <span class="">查看更多</span>
                        <i class="iconfont icon-fangxiang-jiantouyou text-2xl vertical-gradient-text"></i>
                    </a>
                </div>

                <div
                    class="bg-gradient-to-t from-[#0a081c] to-[#1d192c] backdrop-blur-sm border border-[#292638] rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300 bg-[url(/img/Recommendation_05.png)] bg-no-repeat bg-size-[100%] flex flex-col gap-10">
                    <div class="flex-1">
                        <!-- 图标 -->
                        <div class="mb-6">
                            <span class="flex w-[4rem] h-[4rem] bg-[#343045] rounded-2xl items-center justify-center">
                                <!-- <i class="iconfont icon-kimi-copy text-4xl vertical-gradient-text"></i> -->
                                <img src="/img/icon_5.png" alt="">
                            </span>

                        </div>

                        <!-- 标题 -->
                        <h3 class="text-2xl font-bold mb-6">效率突破</h3>

                        <!-- 优势列表 -->
                        <div class="flex flex-col">
                            <p class="leading-relaxed">
                                设计结构化提示模板（如“生成3条科技风广告文案，突出续航和轻薄，字数≤20字”）。

                            </p>
                        </div>
                    </div>
                    <a href="" class="flex items-center gap-2">
                        <span class="">查看更多</span>
                        <i class="iconfont icon-fangxiang-jiantouyou text-2xl vertical-gradient-text"></i>
                    </a>
                </div>
            </div>
        </div>

    </main>
    <footer
        class="py-10 bg-[url(/img/footer_bg.png)] bg-no-repeat bg-[center_bottom] flex flex-col items-center gap-y-8 md:pt-20 bg-[#030015] border-t border-[#343045]">
        <a href="">
            <img src="/img/footer_logo.png" alt="" />
        </a>
        <p class="uppercase text-[#bfc4ce] text-sm">
            © 2025 yuanyi, Inc. All rights reserved
        </p>
    </footer>
    <script src="/js/vendors/jquery-1.8.3.min.js"></script>
    <script src="/js/main.js"></script>

</body>

</html>