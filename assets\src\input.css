@import "tailwindcss";

/* 基础优化样式 */
html,
body {
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-family: 'SourceHanSans_Regular', ui-sans-serif, system-ui, sans-serif;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 自定义基础样式 */
@layer base {

  /* 根字号响应式系统 - 基于分辨率自动调整 */
  html {
    scroll-behavior: smooth;
    /* 默认字号 */
    font-size: 16px;
  }

  /* 小屏设备 (≤1366px) */
  @media screen and (max-width: 1366px) {
    html {
      font-size: 14px;
    }
  }

  /* 中等屏幕 (1367px - 1600px) */
  @media screen and (min-width: 1367px) and (max-width: 1600px) {
    html {
      font-size: 15px;
    }
  }

  /* 标准1080p (1601px - 1920px) */
  @media screen and (min-width: 1601px) and (max-width: 1920px) {
    html {
      font-size: 16px;
    }
  }

  /* 2K显示器 (1921px - 2560px) */
  @media screen and (min-width: 1921px) and (max-width: 2560px) {
    html {
      font-size: 18px;
    }
  }

  /* 超宽屏/2K+ (2561px - 3440px) */
  @media screen and (min-width: 2561px) and (max-width: 3440px) {
    html {
      font-size: 20px;
    }
  }

  /* 4K及以上 (≥3441px) */
  @media screen and (min-width: 3441px) {
    html {
      font-size: 22px;
    }
  }

  /* 系统缩放适配 - 在根字号基础上进行微调 */
  @media screen and (-webkit-device-pixel-ratio: 1.25) {
    html {
      font-size: calc(1em * 0.9);
    }
  }

  @media screen and (-webkit-device-pixel-ratio: 1.5) {
    html {
      font-size: calc(1em * 0.85);
    }
  }

  @media screen and (-webkit-device-pixel-ratio: 1.75) {
    html {
      font-size: calc(1em * 0.8);
    }
  }

  @media screen and (-webkit-device-pixel-ratio: 2) {
    html {
      font-size: calc(1em * 0.75);
    }
  }

  body {
    font-family: 'SourceHanSans_Regular', ui-sans-serif, system-ui, sans-serif;
  }
}

@font-face {
  font-family: "SourceHanSans_Bold";
  src: url(/fonts/SourceHanSans-Bold.woff2);
}

@font-face {
  font-family: "SourceHanSans_Regular";
  src: url(/fonts/SourceHanSans-Regular.woff2);
}

.SourceHanSans_Bold {
  font-family: "SourceHanSans_Bold";
}

.SourceHanSans_Regular {
  font-family: "SourceHanSans_Regular";
}

/* 自定义组件样式 */
@layer components {

  /* 导航样式 */
  .nav-link {
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
    font-weight: 400;
    letter-spacing: 0.025em;
    padding: .625rem 0;
    border-radius: .375rem;
  }

  .nav-link:hover {
    color: #c084fc;
    transform: translateY(-1px);
  }

  /* 当前页面样式 */
  .nav-link.active {
    color: #ffffff;
    font-weight: 500;
    background-color: #4508a0;
  }

  /* 导航列表样式重置 */
  nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  nav li {
    margin: 0;
    padding: 0;
  }

  /* 移动端导航样式 */
  .mobile-nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-decoration: none;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
  }

  .mobile-nav-link:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
    padding-left: 3rem;
  }

  /* 移动端导航列表 */
  #mobile-menu nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  #mobile-menu nav li {
    margin: 0;
    padding: 0;
  }

  /* 导航容器增强 */
  nav[role="navigation"] {
    height: 100%;
  }


  .banner-container {
    background: linear-gradient(-45deg, rgb(238, 119, 82), rgb(231, 60, 126), rgb(35, 166, 213), rgb(35, 213, 171)) 0% 0% / 400% 400%;
    animation: 15s ease 0s infinite normal none running Gradient-18a46d72;
  }

  @keyframes Gradient-18a46d72 {

    0% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0% 50%;
    }

  }

  .wave-container {
    width: 100vw;
    /* height: 50vh; */
    position: relative;
    /* background: #000;  */
    background: url('/img/banner-logo-x-m.png') no-repeat center 90%;
    background-size: 100%;
  }

  .wave {
    position: absolute;
    bottom: 0;
    z-index: -1;
    top: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100vmax;
    /* 确保波纹覆盖整个屏幕 */
    height: 100vmax;
    border-radius: 50%;
    opacity: 0;
    background: radial-gradient(circle at center,
        var(--color),
        transparent 70%);
    animation:
      breath 4s infinite ease-in-out,
      ripple 4s infinite ease-out;
  }

  /* 分别设置4种颜色 */
  .wave-1 {
    --color: rgba(238, 119, 82, 0.7);
    animation-delay: 0s;
  }

  .wave-2 {
    --color: rgba(231, 60, 126, 0.7);
    animation-delay: 1s;
  }

  .wave-3 {
    --color: rgba(35, 166, 213, 0.7);
    animation-delay: 2s;
  }

  .wave-4 {
    --color: rgba(35, 213, 171, 0.7);
    animation-delay: 3s;
  }

  /* 呼吸效果（透明度变化） */
  @keyframes breath {

    0%,
    100% {
      opacity: 0;
    }

    50% {
      opacity: 1;
    }
  }

  /* 波纹扩散效果 */
  @keyframes ripple {
    0% {
      transform: translate(-50%, -50%) scale(0);
    }

    100% {
      transform: translate(-50%, -50%) scale(1);
    }
  }

  /* GEO标签样式 */
  .geo-tag {
    /* 内阴影效果 */
    box-shadow:
      inset 0px 0px .4375rem .1875rem rgba(110, 90, 152, .5);
  }

.geo-card-container::after{
  content: '';
  position: absolute;
  width: 20px;
  height: 100%;
  background: rgb(3,0,21);
  background: linear-gradient(183deg, rgba(3,0,21,0) 0%, rgba(5,0,24,1) 100%);
  left: -5px;
  top: 0;
  z-index: 1;
}

.geo-card-container::before{
  content: '';
  position: absolute;
  width: 20px;
  height: 100%;
  background: rgb(3,0,21);
  background: linear-gradient(183deg, rgba(3,0,21,0) 0%, rgba(5,0,24,1) 100%);
  right: -5px;
  top: 0;
  z-index: 1;
}

  /* 优化项目样式 */
  .optimization-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    @apply flex-1/2 h-[22.5rem] pt-10 md:flex-1/3
  }

  .optimization-item:hover {
    transform: translateY(-5px);
  }

  .optimization-item .group:hover {
    box-shadow:
      0 20px 40px -12px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(147, 51, 234, 0.1);
  }

  /* 渐变分割线 */
  .gradient-divider-vertical {
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(204, 204, 204, 0.5) 5%,
        rgba(204, 204, 204, 0.8) 30%,
        rgba(204, 204, 204, 1) 50%,
        rgba(204, 204, 204, 0.8) 70%,
        rgba(204, 204, 204, 0.5) 90%,
        transparent 100%);
    z-index: 1;
    width: 2px;
  }

  .gradient-divider-horizontal {
    background: linear-gradient(to right,
        transparent 0%,
        rgba(204, 204, 204, 0.5) 15%,
        rgba(204, 204, 204, 1) 50%,
        rgba(204, 204, 204, 0.8) 70%,
        transparent 100%);
    z-index: 1;
    height: 2px;
  }





  .vertical-gradient-text {
    /* 使用您提供的渐变色 */
    background: rgb(255, 255, 255);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.9416141456582633) 0%, rgba(234, 176, 255, 1) 50%, rgba(56, 156, 255, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    text-shadow: none;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .comparison-text {
    display: block;
    font-style: italic;
    background: rgb(127, 41, 255);
    background: linear-gradient(0deg, rgba(127, 41, 255, 1) 0%, rgba(45, 94, 255, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    text-shadow: none;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    @apply w-[2rem] md:w-[4rem]
  }

  /* 浏览器兼容性处理 */
  @supports not ((background-clip: text) or (-webkit-background-clip: text)) {

    .vertical-gradient-text,
    .comparison-text {
      /* 降级方案：使用纯白色 */
      color: #ffffff !important;
      background: none !important;
    }
  }





  /* GEO文字效果 */
  .geo-text {
    display: inline-block;
    font-weight: 900;
    letter-spacing: 0.05em;
    color: transparent;
    -webkit-text-stroke: 1px #9893af;
    position: relative;
  }

  .text_1 {
    display: inline-block;
    font-weight: 900;
    letter-spacing: 0.05em;
    position: relative;
  }

  .geo-text .first-letter {
    color: #9893af;
    -webkit-text-stroke: 0;
    position: relative;
    z-index: 1;
    background: linear-gradient(180deg, #ffffff 0%, #f5f0ff 70%, #43149a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }


}

/* 自定义工具类 */
@layer utilities {

  /* 防止背景滚动 */
  .overflow-hidden {
    overflow: hidden;
  }




  .geo-cta-button {
    transform: translateY(0);
    transition: all 0.3s ease;
  }

  .geo-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(147, 51, 234, 0.2);
  }

  .geo-value-card {
    transition: all 0.3s ease;
  }

  .geo-value-card:hover {
    transform: translateY(-2px);
    border-color: #c084fc;
  }



  /* GEO服务样式 */
  .geo-services-section {
    /* background: #030015; */
  }

  .service-tab {
    min-width: 12.5rem;
    white-space: nowrap;

  }

  .service-tab.active {
    background: linear-gradient(90deg, rgba(111, 34, 182, 1) 0%, rgba(27, 18, 53, 1) 100%) !important;
    border-color: #8b5cf6 !important;
    color: white !important;
    opacity: 1;
  }

  .service-tab:not(.active) {
    background: #030015 !important;
    border-color: #725e9b !important;
    color: #fff !important;
    opacity: 0.5;
  }

  .service-content {
    /* background: #09051d; */
    backdrop-filter: blur(0.625rem);
    border: 2px solid #725e9b;
  }

  .service-content:hover {
    border-color: rgba(139, 92, 246, 0.5);
  }

  /* 中心光晕效果 */
  .radial-spotlight {
    background: radial-gradient(circle at center,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 30%,
        transparent 70%);
  }

  /* 角落渐变遮罩容器 */
  .gradient-overlay-container {
    position: relative;
    overflow: hidden;
  }

  .gradient-overlay-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 0, 0, 0.8) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.8) 0%, transparent 50%),
      radial-gradient(circle at 20% 80%, rgba(0, 0, 0, 0.8) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 0, 0, 0.8) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
  }

  /* GEO案例卡片样式 */
  .case-card {
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }

  .case-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.15);
  }

  .case-card img {
    transition: transform 0.3s ease;
  }

  .case-card:hover img {
    transform: scale(1.05);
  }

  /* GEO服务区域样式 */
  .geo-services-section {
    position: relative;
    overflow: hidden;
  }

  /* GEO新闻区域样式 */

  .news-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .news-item:hover {
    transform: translateY(-2px);
    /* box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); */
  }

  /* 新闻图片容器样式 */
  .news-item .flex-shrink-0 {
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .news-item:hover .flex-shrink-0 {
    transform: scale(1.02);
  }

  /* 新闻图片悬停效果 */
  .news-item img {
    transition: transform 0.3s ease;
  }

  .news-item:hover img {
    transform: scale(1.1);
  }

  /* 新闻链接样式 */
  .news-item {
    text-decoration: none;
    color: inherit;
  }

  .news-item:hover {
    text-decoration: none;
    color: inherit;
  }

}

.iconfont-telephone{
  @apply md:text-2xl;
}

.rq-telephone{
  @apply md:text-5xl;
}

/* 编辑器内容样式 - 确保与网站主题一致 */
.editor-content {
  color: #e0e0e0;
  line-height: 1.8;
}

.editor-content p {
  margin-bottom: 1.5rem;
  color: #e0e0e0;
  line-height: 1.8;
}

.editor-content p:last-child {
  margin-bottom: 0;
}

.editor-content img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.editor-content h1,
.editor-content h2,
.editor-content h3,
.editor-content h4,
.editor-content h5,
.editor-content h6 {
  color: #ffffff;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.editor-content h1 { font-size: 2.25rem; }
.editor-content h2 { font-size: 1.875rem; }
.editor-content h3 { font-size: 1.5rem; }
.editor-content h4 { font-size: 1.25rem; }
.editor-content h5 { font-size: 1.125rem; }
.editor-content h6 { font-size: 1rem; }

.editor-content ul,
.editor-content ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
  color: #e0e0e0;
}

.editor-content li {
  margin-bottom: 0.5rem;
  line-height: 1.8;
}

.editor-content blockquote {
  border-left: 4px solid #6b46c1;
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #c0c0c0;
  background: rgba(107, 70, 193, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 8px;
}

.editor-content code {
  background: rgba(107, 70, 193, 0.2);
  color: #e0e0e0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.editor-content pre {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #343045;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
  overflow-x: auto;
}

.editor-content pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  color: #e0e0e0;
}

.editor-content a {
  color: #8b5cf6;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.editor-content a:hover {
  color: #a78bfa;
}

.editor-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.editor-content th,
.editor-content td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #343045;
}

.editor-content th {
  background: rgba(107, 70, 193, 0.2);
  color: #ffffff;
  font-weight: 600;
}

.editor-content td {
  color: #e0e0e0;
}

.editor-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, #343045, transparent);
  margin: 3rem 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .editor-content {
    font-size: 0.9rem;
  }

  .editor-content h1 { font-size: 1.875rem; }
  .editor-content h2 { font-size: 1.5rem; }
  .editor-content h3 { font-size: 1.25rem; }

  .editor-content img {
    margin: 1.5rem 0;
  }

  .editor-content ul,
  .editor-content ol {
    padding-left: 1.5rem;
  }
}

.inner-glow {
    box-shadow: 
        inset 0 0 2.5rem rgba(110, 90, 152, 0.3), /* 主内发光 */
        inset 0 0 1.875rem rgba(114, 94, 155, 0.2); 
}

.bg-custom-gradient {
  background: linear-gradient(0deg, rgba(255,255,255,1) 0%, rgba(234,176,255,1) 24%, rgba(56,156,255,0.7511379551820728) 100%);
}
