# 并行宇宙 - Parallel Universe

一个使用 HTML5、CSS3、jQuery 和 Tailwind CSS 构建的现代响应式网站。

## 🚀 特性

- **响应式设计**: 完美适配桌面、平板和移动设备
- **现代化界面**: 使用玻璃态效果和渐变背景
- **流畅动画**: 平滑的过渡效果和交互动画
- **Tailwind CSS**: 使用最新的 Tailwind CSS v4.1.11
- **jQuery 增强**: 丰富的交互功能
- **模块化代码**: 清晰的代码结构和组织

## 📁 项目结构

```
GEO并行宇宙/
├── index.html              # 主页面
├── css/
│   └── style.css           # 编译后的样式文件
├── js/
│   └── main.js             # 主要 JavaScript 功能
├── assets/
│   └── src/
│       └── input.css       # Tailwind 源文件
├── img/                    # 图片资源
├── tailwind.config.js      # Tailwind 配置
├── package.json            # 项目配置
└── README.md              # 项目说明
```

## 🛠️ 安装和使用

### 1. 安装依赖

```bash
npm install
```

### 2. 开发模式

启动开发模式，自动监听文件变化并重新编译 CSS：

```bash
npm run dev
```

### 3. 构建生产版本

```bash
npm run build
```

### 4. 在浏览器中打开

直接在浏览器中打开 `index.html` 文件即可查看网站。

## 🎨 设计特色

### 响应式布局
- **移动优先**: 从小屏幕开始设计，逐步增强
- **断点设置**: 
  - `xs`: 475px+
  - `sm`: 640px+
  - `md`: 768px+
  - `lg`: 1024px+
  - `xl`: 1280px+
  - `2xl`: 1536px+

### 视觉效果
- **渐变背景**: 深紫色到蓝色的宇宙渐变
- **玻璃态效果**: 半透明背景配合模糊效果
- **悬浮动画**: 鼠标悬停时的平滑过渡
- **文字渐变**: 标题使用渐变色彩

### 交互功能
- **移动端菜单**: 响应式导航菜单
- **平滑滚动**: 锚点链接的平滑滚动效果
- **动态导航栏**: 滚动时背景透明度变化
- **返回顶部**: 自动显示/隐藏的返回顶部按钮

## 🔧 自定义配置

### Tailwind CSS 配置

在 `tailwind.config.js` 中可以自定义：
- 颜色主题
- 字体设置
- 间距规则
- 动画效果
- 断点设置

### 自定义样式

在 `assets/src/input.css` 中添加自定义样式：
- 组件样式 (`@layer components`)
- 工具类 (`@layer utilities`)
- 基础样式 (`@layer base`)

## 📱 响应式特性

### 移动端优化
- 触摸友好的按钮大小
- 移动端专用导航菜单
- 优化的字体大小和间距
- 手势支持

### 桌面端增强
- 悬浮效果
- 更大的视觉元素
- 多列布局
- 鼠标交互优化

## 🌟 主要组件

### 导航栏
- 固定定位，始终可见
- 响应式菜单切换
- 滚动时背景变化
- 品牌标识和联系信息

### Hero 区域
- 全屏高度设计
- 渐变文字效果
- 行动号召按钮
- 居中对齐布局

### 服务卡片
- 网格布局
- 悬浮动画效果
- 图标和描述
- 响应式列数

## 🚀 性能优化

- **CSS 优化**: 使用 Tailwind 的 purge 功能移除未使用的样式
- **图片懒加载**: 延迟加载图片资源
- **平滑动画**: 使用 CSS transform 而非改变布局属性
- **代码分离**: JavaScript 和 CSS 分离加载

## 🔍 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 📝 开发说明

### 添加新页面
1. 复制 `index.html` 结构
2. 修改内容区域
3. 更新导航链接

### 添加新组件
1. 在 `assets/src/input.css` 中定义样式
2. 在 `js/main.js` 中添加交互逻辑
3. 重新编译 CSS

### 自定义主题
1. 修改 `tailwind.config.js` 中的颜色配置
2. 更新 CSS 变量
3. 重新编译样式

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

- 电话: 400-681-8208
- 网站: [您的网站地址]
- 邮箱: [您的邮箱地址]

---

**并行宇宙** - 探索无限可能的平行世界 🌌
