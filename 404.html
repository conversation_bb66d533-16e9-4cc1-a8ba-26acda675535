<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <!-- 高DPI显示优化 -->
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title>并行宇宙404 - 页面不存在</title>
    <link rel="shortcut icon" href="/favicon.ico">
    </link>
    <!-- 字体图标 -->
    <link rel="stylesheet" href="/css/iconfont.css" />
    <link href="/css/style.css" rel="stylesheet">
</head>

<body class="min-h-screen">
    <!-- 主导航栏 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-[#030015]  border-b border-[#3e3e3e]" id="navbar">
        <div class="md:max-w-10/12 mx-auto px-4 lg:px-0">
            <div class="flex items-center justify-between h-16 lg:h-[6.25rem]">
                <!-- Logo -->
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>

                <!-- 桌面导航菜单 -->
                <nav class="hidden lg:block h-full lg:flex-1 lg:ml-12" role="navigation" aria-label="主导航">
                    <ul class="flex items-center h-full space-x-8">
                        <li class="h-full flex items-center justify-center">
                            <a href="/" class="nav-link flex items-center px-5">
                                首页
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="/services" class="nav-link flex items-center px-5">
                                服务
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                策略
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                案例
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                FAQ
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                关于我们
                            </a>
                        </li>
                        <li class="h-full flex items-center justify-center">
                            <a href="" class="nav-link flex items-center px-5">
                                联系我们
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- 联系信息和二维码 -->
                <div class="hidden lg:flex items-center gap-10">
                    <div class="text-white text-sm md:text-base">
                        <span class="flex items-center gap-2">
                            <i class="iconfont icon-weibiaoti- iconfont-telephone"></i>
                            ************
                        </span>
                    </div>
                    <div
                        class="w-[3.75rem] h-[3.75rem] border-2 border-[#725e9b] rounded-xl flex items-center justify-center cursor-pointer group relative">
                        <i class="iconfont icon-erweima text-white rq-telephone"></i>
                        <div
                            class="border-2 border-[#725e9b] bg-[#272636] absolute top-[120%] min-w-[11.125rem] p-5 hidden group-hover:block">
                            <img src="/img/qr_dow.jpg" class="max-w-full object-cover" alt="">
                        </div>
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="lg:hidden text-white p-2 cursor-pointer">
                    <svg t="1752221010071" class="icon w-6 h-6" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="8317" width="200" height="200">
                        <path
                            d="M153.6 256h716.8a51.2 51.2 0 0 0 0-102.4H153.6a51.2 51.2 0 0 0 0 102.4zM665.6 460.8H153.6a51.2 51.2 0 0 0 0 102.4h512a51.2 51.2 0 0 0 0-102.4zM460.8 768H153.6a51.2 51.2 0 0 0 0 102.4h307.2a51.2 51.2 0 0 0 0-102.4z"
                            fill="#ffffff" p-id="8318"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="lg:hidden fixed inset-0 backdrop-blur-xl z-50 hidden h-lvh bg-[#030015]">
            <!-- 菜单头部 -->
            <div class="flex items-center justify-between px-4 border-b border-white/10 h-16">
                <div class="flex items-center space-x-3 ">
                    <a href="/">
                        <img src="/img/logo.png" class="h-[4rem] md:h-[6.25rem]" alt="">
                    </a>
                </div>
                <button id="mobile-menu-close"
                    class="text-white p-2 hover:bg-white/10 rounded-full transition-colors duration-200">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- 菜单内容 -->
            <div class="flex-1 flex flex-col justify-center">
                <nav class="py-8" role="navigation" aria-label="移动端导航">
                    <ul class="space-y-0">
                        <li>
                            <a href="/"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">首页</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/services"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">服务</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/guide"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">策略</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/cases"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">案例</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/faq"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">FAQ</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/about"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">关于我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                        <li>
                            <a href="/contact"
                                class="mobile-nav-link flex items-center justify-between px-8 py-6 text-white hover:bg-white/10 hover:pl-12 transition-all duration-200 border-b border-[#302c46] group">
                                <span class="text-xl font-medium">联系我们</span>
                                <svg class="w-6 h-6 text-gray-400 group-hover:text-white transition-colors duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 菜单底部联系信息 -->
            <div class="p-8 border-t border-white/10">
                <div class="flex items-center justify-center space-x-4 text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z" />
                    </svg>
                    <span class="text-xl font-medium">************</span>
                </div>
            </div>
        </div>
    </header>

    <main
        class="pt-16 lg:pt-[6.25rem] min-h-screen bg-[#030015] text-white bg-[url(/img/404_bg_m.png)] bg-no-repeat bg-[center_bottom] bg-size-[100%_auto] h-full md:bg-[url(/img/404_bg.png)] md:bg-size-[90%] md:h-[75rem]">
        <div class="flex flex-col items-center text-center h-lvh pt-[11.25rem] md:pt-[9.375rem]">
            <div class="text-[#afaac6] text-xl mb-[2.5rem] md:text-3xl">
                <p>
                    哎呀，出错了！您访问的页面不存在......
                </p>
            </div>
            <div class="flex flex-col justify-center items-center mb-[1.875rem]">
                <div class="w-[10rem] mb-[4.375rem] md:w-[12.5rem]">
                    <img src="/img/404.png" alt="404" class="w-full h-full object-cover" />
                </div>
                <div class="text-[#afaac6] text-lg">
                    您可以通过以下方式继续访问：
                </div>
            </div>
            <div class="flex justify-center gap-x-10">
                <a href="" class="border-2 border-[#afaac6] py-3 px-14 rounded-full">访问首页</a>
                <a href="" class="border-2 border-[#afaac6] py-3 px-14 rounded-full">返回上一步</a>
            </div>
        </div>
    </main>
    <footer
        class="py-10 bg-[url(/img/footer_bg.png)] bg-no-repeat bg-[center_bottom] flex flex-col items-center gap-y-8 md:pt-20 bg-[#030015] border-t border-[#343045]">
        <a href="">
            <i class="iconfont icon-binghangyuzhou2 text-4xl md:text-5xl vertical-gradient-text"></i>
        </a>
        <p class="uppercase text-[#bfc4ce] text-sm">
            © 2025 yuanyi, Inc. All rights reserved
        </p>
    </footer>
    <script src="/js/vendors/jquery-1.8.3.min.js"></script>
    <script src="/js/main.js"></script>
</body>

</html>