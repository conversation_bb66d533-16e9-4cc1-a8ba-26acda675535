/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-purple-50: oklch(97.7% 0.014 308.299);
    --color-purple-200: oklch(90.2% 0.063 306.703);
    --color-purple-300: oklch(82.7% 0.119 306.383);
    --color-purple-400: oklch(71.4% 0.203 305.504);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-purple-600: oklch(55.8% 0.288 302.321);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-3xs: 16rem;
    --container-2xs: 18rem;
    --container-xs: 20rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --blur-sm: 8px;
    --blur-xl: 24px;
    --aspect-video: 16 / 9;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .-top-2 {
    top: calc(var(--spacing) * -2);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-1\/3 {
    top: calc(1/3 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-2\/3 {
    top: calc(2/3 * 100%);
  }
  .top-\[120\%\] {
    top: 120%;
  }
  .-right-2 {
    right: calc(var(--spacing) * -2);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-8 {
    right: calc(var(--spacing) * 8);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1 {
    left: calc(var(--spacing) * 1);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-1\/3 {
    left: calc(1/3 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .left-2\/3 {
    left: calc(2/3 * 100%);
  }
  .z-0 {
    z-index: 0;
  }
  .z-10 {
    z-index: 10;
  }
  .z-50 {
    z-index: 50;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-auto {
    margin-inline: auto;
  }
  .-mt-16 {
    margin-top: calc(var(--spacing) * -16);
  }
  .-mt-20 {
    margin-top: calc(var(--spacing) * -20);
  }
  .-mt-\[3\.75rem\] {
    margin-top: calc(3.75rem * -1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-3\.5 {
    margin-bottom: calc(var(--spacing) * 3.5);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .mb-\[1\.875rem\] {
    margin-bottom: 1.875rem;
  }
  .mb-\[1\.5625rem\] {
    margin-bottom: 1.5625rem;
  }
  .mb-\[2\.5rem\] {
    margin-bottom: 2.5rem;
  }
  .mb-\[4\.375rem\] {
    margin-bottom: 4.375rem;
  }
  .mb-\[5rem\] {
    margin-bottom: 5rem;
  }
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-\[1\.875rem\] {
    height: 1.875rem;
  }
  .h-\[3\.75rem\] {
    height: 3.75rem;
  }
  .h-\[4rem\] {
    height: 4rem;
  }
  .h-\[9\.375rem\] {
    height: 9.375rem;
  }
  .h-\[10\.875rem\] {
    height: 10.875rem;
  }
  .h-\[18\.75rem\] {
    height: 18.75rem;
  }
  .h-full {
    height: 100%;
  }
  .h-lvh {
    height: 100lvh;
  }
  .min-h-10 {
    min-height: calc(var(--spacing) * 10);
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2xs {
    width: var(--container-2xs);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-11 {
    width: calc(var(--spacing) * 11);
  }
  .w-11\/12 {
    width: calc(11/12 * 100%);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-28 {
    width: calc(var(--spacing) * 28);
  }
  .w-\[1\.875rem\] {
    width: 1.875rem;
  }
  .w-\[3\.75rem\] {
    width: 3.75rem;
  }
  .w-\[4rem\] {
    width: 4rem;
  }
  .w-\[9\.375rem\] {
    width: 9.375rem;
  }
  .w-\[10\.875rem\] {
    width: 10.875rem;
  }
  .w-\[10rem\] {
    width: 10rem;
  }
  .w-\[12\.5rem\] {
    width: 12.5rem;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xs {
    max-width: var(--container-3xs);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-\[9\.375rem\] {
    max-width: 9.375rem;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-\[11\.125rem\] {
    min-width: 11.125rem;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .-translate-x-1 {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1 {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-rotate-180 {
    rotate: calc(180deg * -1);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-around {
    justify-content: space-around;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }
  .space-y-0 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-10 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 10) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }
  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }
  .gap-x-10 {
    column-gap: calc(var(--spacing) * 10);
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-8 {
    row-gap: calc(var(--spacing) * 8);
  }
  .gap-y-10 {
    row-gap: calc(var(--spacing) * 10);
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-tl-3xl {
    border-top-left-radius: var(--radius-3xl);
  }
  .rounded-tl-4xl {
    border-top-left-radius: var(--radius-4xl);
  }
  .rounded-tr-3xl {
    border-top-right-radius: var(--radius-3xl);
  }
  .rounded-tr-4xl {
    border-top-right-radius: var(--radius-4xl);
  }
  .rounded-br-none {
    border-bottom-right-radius: 0;
  }
  .rounded-bl-none {
    border-bottom-left-radius: 0;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-0 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0px;
  }
  .border-\[\#3d3a5c\]\/50 {
    border-color: color-mix(in oklab, #3d3a5c 50%, transparent);
  }
  .border-\[\#3e3e3e\] {
    border-color: #3e3e3e;
  }
  .border-\[\#7c3aed\] {
    border-color: #7c3aed;
  }
  .border-\[\#302c46\] {
    border-color: #302c46;
  }
  .border-\[\#412d5a\] {
    border-color: #412d5a;
  }
  .border-\[\#423e52\] {
    border-color: #423e52;
  }
  .border-\[\#725e9b\] {
    border-color: #725e9b;
  }
  .border-\[\#292638\] {
    border-color: #292638;
  }
  .border-\[\#343045\] {
    border-color: #343045;
  }
  .border-\[\#afaac6\] {
    border-color: #afaac6;
  }
  .border-\[\#ccc\] {
    border-color: #ccc;
  }
  .border-\[\#cccccc\] {
    border-color: #cccccc;
  }
  .border-\[\#dfc5ff\] {
    border-color: #dfc5ff;
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-600 {
    border-color: var(--color-gray-600);
  }
  .border-gray-700 {
    border-color: var(--color-gray-700);
  }
  .border-purple-200 {
    border-color: var(--color-purple-200);
  }
  .border-purple-500 {
    border-color: var(--color-purple-500);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-white\/10 {
    border-color: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .bg-\[\#0b081d\] {
    background-color: #0b081d;
  }
  .bg-\[\#0d0b1e\] {
    background-color: #0d0b1e;
  }
  .bg-\[\#1f1c2e\] {
    background-color: #1f1c2e;
  }
  .bg-\[\#2a2438\] {
    background-color: #2a2438;
  }
  .bg-\[\#6b46c1\] {
    background-color: #6b46c1;
  }
  .bg-\[\#7f29ff\] {
    background-color: #7f29ff;
  }
  .bg-\[\#030015\] {
    background-color: #030015;
  }
  .bg-\[\#272636\] {
    background-color: #272636;
  }
  .bg-\[\#292638\] {
    background-color: #292638;
  }
  .bg-\[\#343045\] {
    background-color: #343045;
  }
  .bg-\[\#f5f5f5\] {
    background-color: #f5f5f5;
  }
  .bg-\[\#ffffff\] {
    background-color: #ffffff;
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/20 {
    background-color: color-mix(in srgb, #000 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }
  .bg-black\/80 {
    background-color: color-mix(in srgb, #000 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }
  .bg-black\/90 {
    background-color: color-mix(in srgb, #000 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 90%, transparent);
    }
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-l {
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-\[url\(\'\/img\/banner-wg-m\.png\'\)\] {
    background-image: url('/img/banner-wg-m.png');
  }
  .bg-\[url\(\'\/img\/bj-m\.jpg\'\)\] {
    background-image: url('/img/bj-m.jpg');
  }
  .bg-\[url\(\/img\/404_bg_m\.png\)\] {
    background-image: url(/img/404_bg_m.png);
  }
  .bg-\[url\(\/img\/Recommendation_01\.png\)\] {
    background-image: url(/img/Recommendation_01.png);
  }
  .bg-\[url\(\/img\/Recommendation_02\.png\)\] {
    background-image: url(/img/Recommendation_02.png);
  }
  .bg-\[url\(\/img\/Recommendation_03\.png\)\] {
    background-image: url(/img/Recommendation_03.png);
  }
  .bg-\[url\(\/img\/Recommendation_04\.png\)\] {
    background-image: url(/img/Recommendation_04.png);
  }
  .bg-\[url\(\/img\/Recommendation_05\.png\)\] {
    background-image: url(/img/Recommendation_05.png);
  }
  .bg-\[url\(\/img\/about_bg_1\.png\)\] {
    background-image: url(/img/about_bg_1.png);
  }
  .bg-\[url\(\/img\/about_bg_2\.png\)\] {
    background-image: url(/img/about_bg_2.png);
  }
  .bg-\[url\(\/img\/ai_bj_m\.jpg\)\] {
    background-image: url(/img/ai_bj_m.jpg);
  }
  .bg-\[url\(\/img\/bg-2\.png\)\] {
    background-image: url(/img/bg-2.png);
  }
  .bg-\[url\(\/img\/footer_bg\.png\)\] {
    background-image: url(/img/footer_bg.png);
  }
  .bg-\[url\(\/img\/swiper-bg\.png\)\] {
    background-image: url(/img/swiper-bg.png);
  }
  .bg-none {
    background-image: none;
  }
  .from-\[\#0a081c\] {
    --tw-gradient-from: #0a081c;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-\[\#1a1830\]\/80 {
    --tw-gradient-from: color-mix(in oklab, #1a1830 80%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-black {
    --tw-gradient-from: var(--color-black);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-900 {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-900\/50 {
    --tw-gradient-from: color-mix(in srgb, oklch(21% 0.034 264.665) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-gray-900) 50%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-500 {
    --tw-gradient-from: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#0f0d20\]\/80 {
    --tw-gradient-to: color-mix(in oklab, #0f0d20 80%, transparent);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-\[\#1d192c\] {
    --tw-gradient-to: #1d192c;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-500 {
    --tw-gradient-to: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-800 {
    --tw-gradient-to: var(--color-gray-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-800\/30 {
    --tw-gradient-to: color-mix(in srgb, oklch(27.8% 0.033 256.848) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-gray-800) 30%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-size-\[35\.25rem\] {
    background-size: 35.25rem;
  }
  .bg-size-\[100\%\] {
    background-size: 100%;
  }
  .bg-size-\[100\%_auto\] {
    background-size: 100% auto;
  }
  .bg-\[center_bottom\] {
    background-position: center bottom;
  }
  .bg-\[left_top\] {
    background-position: left top;
  }
  .bg-\[right_80\%\] {
    background-position: right 80%;
  }
  .bg-\[right_bottom\] {
    background-position: right bottom;
  }
  .bg-bottom {
    background-position: bottom;
  }
  .bg-center {
    background-position: center;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }
  .px-14 {
    padding-inline: calc(var(--spacing) * 14);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-3\.5 {
    padding-block: calc(var(--spacing) * 3.5);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pt-10 {
    padding-top: calc(var(--spacing) * 10);
  }
  .pt-14 {
    padding-top: calc(var(--spacing) * 14);
  }
  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }
  .pt-\[3\.75rem\] {
    padding-top: 3.75rem;
  }
  .pt-\[11\.25rem\] {
    padding-top: 11.25rem;
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }
  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }
  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .pb-\[3\.25rem\] {
    padding-bottom: 3.25rem;
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-7 {
    --tw-leading: calc(var(--spacing) * 7);
    line-height: calc(var(--spacing) * 7);
  }
  .leading-8 {
    --tw-leading: calc(var(--spacing) * 8);
    line-height: calc(var(--spacing) * 8);
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .text-\[\#8a8f99\] {
    color: #8a8f99;
  }
  .text-\[\#725e9b\] {
    color: #725e9b;
  }
  .text-\[\#999\] {
    color: #999;
  }
  .text-\[\#111111\] {
    color: #111111;
  }
  .text-\[\#818181\] {
    color: #818181;
  }
  .text-\[\#a9a8b9\] {
    color: #a9a8b9;
  }
  .text-\[\#aaaaaa\] {
    color: #aaaaaa;
  }
  .text-\[\#afaac6\] {
    color: #afaac6;
  }
  .text-\[\#bc8bff\] {
    color: #bc8bff;
  }
  .text-\[\#bfc4ce\] {
    color: #bfc4ce;
  }
  .text-\[\#cccccc\] {
    color: #cccccc;
  }
  .text-\[\#d0d6df\] {
    color: #d0d6df;
  }
  .text-\[\#ddd\] {
    color: #ddd;
  }
  .text-\[\#dddddd\] {
    color: #dddddd;
  }
  .text-\[\#eec3ff\] {
    color: #eec3ff;
  }
  .text-\[\#efc3ff\] {
    color: #efc3ff;
  }
  .text-\[\#f3f3f3\] {
    color: #f3f3f3;
  }
  .text-\[\#fff\] {
    color: #fff;
  }
  .text-black {
    color: var(--color-black);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-white {
    color: var(--color-white);
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .underline {
    text-decoration-line: underline;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .opacity-90 {
    opacity: 90%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .delay-200 {
    transition-delay: 200ms;
  }
  .delay-300 {
    transition-delay: 300ms;
  }
  .delay-400 {
    transition-delay: 400ms;
  }
  .delay-500 {
    transition-delay: 500ms;
  }
  .delay-600 {
    transition-delay: 600ms;
  }
  .duration-30 {
    --tw-duration: 30ms;
    transition-duration: 30ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .backface-hidden {
    backface-visibility: hidden;
  }
  .group-hover\:block {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        display: block;
      }
    }
  }
  .group-hover\:scale-101 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 101%;
        --tw-scale-y: 101%;
        --tw-scale-z: 101%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:scale-105 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:rotate-45 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        rotate: 45deg;
      }
    }
  }
  .group-hover\:border-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        border-color: var(--color-white);
      }
    }
  }
  .group-hover\:bg-\[\#1d192c\] {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: #1d192c;
      }
    }
  }
  .group-hover\:text-purple-300 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-purple-300);
      }
    }
  }
  .group-hover\:text-purple-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-purple-600);
      }
    }
  }
  .group-hover\:text-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .group-hover\:opacity-20 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 20%;
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .hover\:border-purple-400 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-purple-400);
      }
    }
  }
  .hover\:border-purple-500 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-purple-500);
      }
    }
  }
  .hover\:border-purple-500\/50 {
    &:hover {
      @media (hover: hover) {
        border-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-purple-500) 50%, transparent);
        }
      }
    }
  }
  .hover\:border-white\/50 {
    &:hover {
      @media (hover: hover) {
        border-color: color-mix(in srgb, #fff 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-white) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-\[\#3a3448\] {
    &:hover {
      @media (hover: hover) {
        background-color: #3a3448;
      }
    }
  }
  .hover\:bg-\[\#7c3aed\] {
    &:hover {
      @media (hover: hover) {
        background-color: #7c3aed;
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-600);
      }
    }
  }
  .hover\:bg-purple-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-50);
      }
    }
  }
  .hover\:bg-purple-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-600);
      }
    }
  }
  .hover\:bg-white\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 10%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
      }
    }
  }
  .hover\:bg-\[url\(\/img\/swiper-bg\.png\)\] {
    &:hover {
      @media (hover: hover) {
        background-image: url(/img/swiper-bg.png);
      }
    }
  }
  .hover\:bg-cover {
    &:hover {
      @media (hover: hover) {
        background-size: cover;
      }
    }
  }
  .hover\:bg-no-repeat {
    &:hover {
      @media (hover: hover) {
        background-repeat: no-repeat;
      }
    }
  }
  .hover\:pl-12 {
    &:hover {
      @media (hover: hover) {
        padding-left: calc(var(--spacing) * 12);
      }
    }
  }
  .hover\:text-\[\#007bff\] {
    &:hover {
      @media (hover: hover) {
        color: #007bff;
      }
    }
  }
  .hover\:text-purple-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-purple-600);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:shadow-purple-500\/25 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 25%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-purple-500) 25%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .md\:top-1\/2 {
    @media (width >= 48rem) {
      top: calc(1/2 * 100%);
    }
  }
  .md\:mx-auto {
    @media (width >= 48rem) {
      margin-inline: auto;
    }
  }
  .md\:mt-10 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 10);
    }
  }
  .md\:mt-\[2\.9375rem\] {
    @media (width >= 48rem) {
      margin-top: 2.9375rem;
    }
  }
  .md\:mt-\[5\.125rem\] {
    @media (width >= 48rem) {
      margin-top: 5.125rem;
    }
  }
  .md\:-mb-\[6\.25rem\] {
    @media (width >= 48rem) {
      margin-bottom: calc(6.25rem * -1);
    }
  }
  .md\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:mb-5 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 5);
    }
  }
  .md\:mb-8 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }
  .md\:mb-10 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 10);
    }
  }
  .md\:mb-14 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 14);
    }
  }
  .md\:mb-16 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }
  .md\:mb-20 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 20);
    }
  }
  .md\:mb-\[1\.875rem\] {
    @media (width >= 48rem) {
      margin-bottom: 1.875rem;
    }
  }
  .md\:mb-\[2\.5rem\] {
    @media (width >= 48rem) {
      margin-bottom: 2.5rem;
    }
  }
  .md\:mb-\[2\.125rem\] {
    @media (width >= 48rem) {
      margin-bottom: 2.125rem;
    }
  }
  .md\:mb-\[3\.75rem\] {
    @media (width >= 48rem) {
      margin-bottom: 3.75rem;
    }
  }
  .md\:mb-\[3\.125rem\] {
    @media (width >= 48rem) {
      margin-bottom: 3.125rem;
    }
  }
  .md\:mb-\[3\.4375rem\] {
    @media (width >= 48rem) {
      margin-bottom: 3.4375rem;
    }
  }
  .md\:mb-\[4\.375rem\] {
    @media (width >= 48rem) {
      margin-bottom: 4.375rem;
    }
  }
  .md\:mb-\[5\.625rem\] {
    @media (width >= 48rem) {
      margin-bottom: 5.625rem;
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:grid {
    @media (width >= 48rem) {
      display: grid;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:inline-block {
    @media (width >= 48rem) {
      display: inline-block;
    }
  }
  .md\:h-\[1\.5625rem\] {
    @media (width >= 48rem) {
      height: 1.5625rem;
    }
  }
  .md\:h-\[2\.5rem\] {
    @media (width >= 48rem) {
      height: 2.5rem;
    }
  }
  .md\:h-\[3\.75rem\] {
    @media (width >= 48rem) {
      height: 3.75rem;
    }
  }
  .md\:h-\[3\.125rem\] {
    @media (width >= 48rem) {
      height: 3.125rem;
    }
  }
  .md\:h-\[4\.375rem\] {
    @media (width >= 48rem) {
      height: 4.375rem;
    }
  }
  .md\:h-\[6\.25rem\] {
    @media (width >= 48rem) {
      height: 6.25rem;
    }
  }
  .md\:h-\[8\.125rem\] {
    @media (width >= 48rem) {
      height: 8.125rem;
    }
  }
  .md\:h-\[17\.0625rem\] {
    @media (width >= 48rem) {
      height: 17.0625rem;
    }
  }
  .md\:h-\[18\.75rem\] {
    @media (width >= 48rem) {
      height: 18.75rem;
    }
  }
  .md\:h-\[34\.375rem\] {
    @media (width >= 48rem) {
      height: 34.375rem;
    }
  }
  .md\:h-\[42\.875rem\] {
    @media (width >= 48rem) {
      height: 42.875rem;
    }
  }
  .md\:h-\[75rem\] {
    @media (width >= 48rem) {
      height: 75rem;
    }
  }
  .md\:h-full {
    @media (width >= 48rem) {
      height: 100%;
    }
  }
  .md\:min-h-\[31\.25rem\] {
    @media (width >= 48rem) {
      min-height: 31.25rem;
    }
  }
  .md\:min-h-screen {
    @media (width >= 48rem) {
      min-height: 100vh;
    }
  }
  .md\:w-1\/3 {
    @media (width >= 48rem) {
      width: calc(1/3 * 100%);
    }
  }
  .md\:w-2\/3 {
    @media (width >= 48rem) {
      width: calc(2/3 * 100%);
    }
  }
  .md\:w-10\/12 {
    @media (width >= 48rem) {
      width: calc(10/12 * 100%);
    }
  }
  .md\:w-\[1\.5625rem\] {
    @media (width >= 48rem) {
      width: 1.5625rem;
    }
  }
  .md\:w-\[2\.5rem\] {
    @media (width >= 48rem) {
      width: 2.5rem;
    }
  }
  .md\:w-\[3\.75rem\] {
    @media (width >= 48rem) {
      width: 3.75rem;
    }
  }
  .md\:w-\[11\.25rem\] {
    @media (width >= 48rem) {
      width: 11.25rem;
    }
  }
  .md\:w-\[11\.875rem\] {
    @media (width >= 48rem) {
      width: 11.875rem;
    }
  }
  .md\:w-\[12\.5rem\] {
    @media (width >= 48rem) {
      width: 12.5rem;
    }
  }
  .md\:w-\[17\.0625rem\] {
    @media (width >= 48rem) {
      width: 17.0625rem;
    }
  }
  .md\:w-\[18\.75rem\] {
    @media (width >= 48rem) {
      width: 18.75rem;
    }
  }
  .md\:w-\[21\.75rem\] {
    @media (width >= 48rem) {
      width: 21.75rem;
    }
  }
  .md\:w-\[25rem\] {
    @media (width >= 48rem) {
      width: 25rem;
    }
  }
  .md\:w-full {
    @media (width >= 48rem) {
      width: 100%;
    }
  }
  .md\:max-w-5xl {
    @media (width >= 48rem) {
      max-width: var(--container-5xl);
    }
  }
  .md\:max-w-10\/12 {
    @media (width >= 48rem) {
      max-width: calc(10/12 * 100%);
    }
  }
  .md\:max-w-max {
    @media (width >= 48rem) {
      max-width: max-content;
    }
  }
  .md\:flex-none {
    @media (width >= 48rem) {
      flex: none;
    }
  }
  .md\:grid-cols-1 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:items-baseline {
    @media (width >= 48rem) {
      align-items: baseline;
    }
  }
  .md\:items-center {
    @media (width >= 48rem) {
      align-items: center;
    }
  }
  .md\:items-end {
    @media (width >= 48rem) {
      align-items: flex-end;
    }
  }
  .md\:gap-5 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 5);
    }
  }
  .md\:gap-10 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 10);
    }
  }
  .md\:space-y-0 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\:gap-x-10 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 10);
    }
  }
  .md\:gap-x-20 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 20);
    }
  }
  .md\:gap-x-\[4\.375rem\] {
    @media (width >= 48rem) {
      column-gap: 4.375rem;
    }
  }
  .md\:gap-y-2 {
    @media (width >= 48rem) {
      row-gap: calc(var(--spacing) * 2);
    }
  }
  .md\:gap-y-5 {
    @media (width >= 48rem) {
      row-gap: calc(var(--spacing) * 5);
    }
  }
  .md\:rounded-tl-4xl {
    @media (width >= 48rem) {
      border-top-left-radius: var(--radius-4xl);
    }
  }
  .md\:rounded-tr-4xl {
    @media (width >= 48rem) {
      border-top-right-radius: var(--radius-4xl);
    }
  }
  .md\:bg-\[url\(\'\/img\/banner-wg\.png\'\)\] {
    @media (width >= 48rem) {
      background-image: url('/img/banner-wg.png');
    }
  }
  .md\:bg-\[url\(\'\/img\/book_bg\.jpg\'\)\] {
    @media (width >= 48rem) {
      background-image: url('/img/book_bg.jpg');
    }
  }
  .md\:bg-\[url\(\/img\/404_bg\.png\)\] {
    @media (width >= 48rem) {
      background-image: url(/img/404_bg.png);
    }
  }
  .md\:bg-\[url\(\/img\/ai_bj\.png\)\] {
    @media (width >= 48rem) {
      background-image: url(/img/ai_bj.png);
    }
  }
  .md\:bg-cover {
    @media (width >= 48rem) {
      background-size: cover;
    }
  }
  .md\:bg-size-\[90\%\] {
    @media (width >= 48rem) {
      background-size: 90%;
    }
  }
  .md\:px-0 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .md\:px-\[2\.5rem\] {
    @media (width >= 48rem) {
      padding-inline: 2.5rem;
    }
  }
  .md\:px-\[3\.125rem\] {
    @media (width >= 48rem) {
      padding-inline: 3.125rem;
    }
  }
  .md\:py-3 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .md\:py-3\.5 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 3.5);
    }
  }
  .md\:py-4 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .md\:py-5 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 5);
    }
  }
  .md\:py-\[1\.875rem\] {
    @media (width >= 48rem) {
      padding-block: 1.875rem;
    }
  }
  .md\:pt-10 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 10);
    }
  }
  .md\:pt-20 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 20);
    }
  }
  .md\:pt-\[3\.75rem\] {
    @media (width >= 48rem) {
      padding-top: 3.75rem;
    }
  }
  .md\:pt-\[4\.375rem\] {
    @media (width >= 48rem) {
      padding-top: 4.375rem;
    }
  }
  .md\:pt-\[6\.25rem\] {
    @media (width >= 48rem) {
      padding-top: 6.25rem;
    }
  }
  .md\:pt-\[9\.375rem\] {
    @media (width >= 48rem) {
      padding-top: 9.375rem;
    }
  }
  .md\:pt-\[10rem\] {
    @media (width >= 48rem) {
      padding-top: 10rem;
    }
  }
  .md\:pr-14 {
    @media (width >= 48rem) {
      padding-right: calc(var(--spacing) * 14);
    }
  }
  .md\:pb-0 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:pb-5 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 5);
    }
  }
  .md\:pb-10 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 10);
    }
  }
  .md\:pb-24 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 24);
    }
  }
  .md\:pb-\[1\.875rem\] {
    @media (width >= 48rem) {
      padding-bottom: 1.875rem;
    }
  }
  .md\:pb-\[6\.25rem\] {
    @media (width >= 48rem) {
      padding-bottom: 6.25rem;
    }
  }
  .md\:pb-\[6\.875rem\] {
    @media (width >= 48rem) {
      padding-bottom: 6.875rem;
    }
  }
  .md\:pb-\[7\.5rem\] {
    @media (width >= 48rem) {
      padding-bottom: 7.5rem;
    }
  }
  .md\:pb-\[11\.25rem\] {
    @media (width >= 48rem) {
      padding-bottom: 11.25rem;
    }
  }
  .md\:pl-\[2\.5rem\] {
    @media (width >= 48rem) {
      padding-left: 2.5rem;
    }
  }
  .md\:text-2xl {
    @media (width >= 48rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .md\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .md\:text-5xl {
    @media (width >= 48rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .md\:text-6xl {
    @media (width >= 48rem) {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
  .md\:text-7xl {
    @media (width >= 48rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
  .md\:text-8xl {
    @media (width >= 48rem) {
      font-size: var(--text-8xl);
      line-height: var(--tw-leading, var(--text-8xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 48rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .md\:text-\[1\.125rem\] {
    @media (width >= 48rem) {
      font-size: 1.125rem;
    }
  }
  .md\:text-\[2\.5rem\] {
    @media (width >= 48rem) {
      font-size: 2.5rem;
    }
  }
  .md\:text-\[4\.375rem\] {
    @media (width >= 48rem) {
      font-size: 4.375rem;
    }
  }
  .md\:leading-10 {
    @media (width >= 48rem) {
      --tw-leading: calc(var(--spacing) * 10);
      line-height: calc(var(--spacing) * 10);
    }
  }
  .md\:text-\[\#aaaaaa\] {
    @media (width >= 48rem) {
      color: #aaaaaa;
    }
  }
  .lg\:ml-12 {
    @media (width >= 64rem) {
      margin-left: calc(var(--spacing) * 12);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:h-\[6\.25rem\] {
    @media (width >= 64rem) {
      height: 6.25rem;
    }
  }
  .lg\:flex-1 {
    @media (width >= 64rem) {
      flex: 1;
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:justify-start {
    @media (width >= 64rem) {
      justify-content: flex-start;
    }
  }
  .lg\:px-0 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:pt-20 {
    @media (width >= 64rem) {
      padding-top: calc(var(--spacing) * 20);
    }
  }
  .lg\:pt-\[6\.25rem\] {
    @media (width >= 64rem) {
      padding-top: 6.25rem;
    }
  }
  .lg\:text-left {
    @media (width >= 64rem) {
      text-align: left;
    }
  }
  .lg\:text-2xl {
    @media (width >= 64rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .lg\:text-5xl {
    @media (width >= 64rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .lg\:text-6xl {
    @media (width >= 64rem) {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
  .lg\:text-7xl {
    @media (width >= 64rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
}
html, body {
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-family: 'SourceHanSans_Regular', ui-sans-serif, system-ui, sans-serif;
}
*, *::before, *::after {
  box-sizing: border-box;
}
@layer base {
  html {
    scroll-behavior: smooth;
    font-size: 16px;
  }
  @media screen and (max-width: 1366px) {
    html {
      font-size: 14px;
    }
  }
  @media screen and (min-width: 1367px) and (max-width: 1600px) {
    html {
      font-size: 15px;
    }
  }
  @media screen and (min-width: 1601px) and (max-width: 1920px) {
    html {
      font-size: 16px;
    }
  }
  @media screen and (min-width: 1921px) and (max-width: 2560px) {
    html {
      font-size: 18px;
    }
  }
  @media screen and (min-width: 2561px) and (max-width: 3440px) {
    html {
      font-size: 20px;
    }
  }
  @media screen and (min-width: 3441px) {
    html {
      font-size: 22px;
    }
  }
  @media screen and (-webkit-device-pixel-ratio: 1.25) {
    html {
      font-size: calc(1em * 0.9);
    }
  }
  @media screen and (-webkit-device-pixel-ratio: 1.5) {
    html {
      font-size: calc(1em * 0.85);
    }
  }
  @media screen and (-webkit-device-pixel-ratio: 1.75) {
    html {
      font-size: calc(1em * 0.8);
    }
  }
  @media screen and (-webkit-device-pixel-ratio: 2) {
    html {
      font-size: calc(1em * 0.75);
    }
  }
  body {
    font-family: 'SourceHanSans_Regular', ui-sans-serif, system-ui, sans-serif;
  }
}
@font-face {
  font-family: "SourceHanSans_Bold";
  src: url(/fonts/SourceHanSans-Bold.woff2);
}
@font-face {
  font-family: "SourceHanSans_Regular";
  src: url(/fonts/SourceHanSans-Regular.woff2);
}
.SourceHanSans_Bold {
  font-family: "SourceHanSans_Bold";
}
.SourceHanSans_Regular {
  font-family: "SourceHanSans_Regular";
}
@layer components {
  .nav-link {
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
    font-weight: 400;
    letter-spacing: 0.025em;
    padding: .625rem 0;
    border-radius: .375rem;
  }
  .nav-link:hover {
    color: #c084fc;
    transform: translateY(-1px);
  }
  .nav-link.active {
    color: #ffffff;
    font-weight: 500;
    background-color: #4508a0;
  }
  nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  nav li {
    margin: 0;
    padding: 0;
  }
  .mobile-nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-decoration: none;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
  }
  .mobile-nav-link:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
    padding-left: 3rem;
  }
  #mobile-menu nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  #mobile-menu nav li {
    margin: 0;
    padding: 0;
  }
  nav[role="navigation"] {
    height: 100%;
  }
  .banner-container {
    background: linear-gradient(-45deg, rgb(238, 119, 82), rgb(231, 60, 126), rgb(35, 166, 213), rgb(35, 213, 171)) 0% 0% / 400% 400%;
    animation: 15s ease 0s infinite normal none running Gradient-18a46d72;
  }
  @keyframes Gradient-18a46d72 {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  .wave-container {
    width: 100vw;
    position: relative;
    background: url('/img/banner-logo-x-m.png') no-repeat center 90%;
    background-size: 100%;
  }
  .wave {
    position: absolute;
    bottom: 0;
    z-index: -1;
    top: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100vmax;
    height: 100vmax;
    border-radius: 50%;
    opacity: 0;
    background: radial-gradient(circle at center, var(--color), transparent 70%);
    animation: breath 4s infinite ease-in-out, ripple 4s infinite ease-out;
  }
  .wave-1 {
    --color: rgba(238, 119, 82, 0.7);
    animation-delay: 0s;
  }
  .wave-2 {
    --color: rgba(231, 60, 126, 0.7);
    animation-delay: 1s;
  }
  .wave-3 {
    --color: rgba(35, 166, 213, 0.7);
    animation-delay: 2s;
  }
  .wave-4 {
    --color: rgba(35, 213, 171, 0.7);
    animation-delay: 3s;
  }
  @keyframes breath {
    0%, 100% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }
  @keyframes ripple {
    0% {
      transform: translate(-50%, -50%) scale(0);
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
    }
  }
  .geo-tag {
    box-shadow: inset 0px 0px .4375rem .1875rem rgba(110, 90, 152, .5);
  }
  .geo-card-container::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 100%;
    background: rgb(3,0,21);
    background: linear-gradient(183deg, rgba(3,0,21,0) 0%, rgba(5,0,24,1) 100%);
    left: -5px;
    top: 0;
    z-index: 1;
  }
  .geo-card-container::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 100%;
    background: rgb(3,0,21);
    background: linear-gradient(183deg, rgba(3,0,21,0) 0%, rgba(5,0,24,1) 100%);
    right: -5px;
    top: 0;
    z-index: 1;
  }
  .optimization-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 22.5rem;
    flex: calc(1/2 * 100%);
    padding-top: calc(var(--spacing) * 10);
    @media (width >= 48rem) {
      flex: calc(1/3 * 100%);
    }
  }
  .optimization-item:hover {
    transform: translateY(-5px);
  }
  .optimization-item .group:hover {
    box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(147, 51, 234, 0.1);
  }
  .gradient-divider-vertical {
    background: linear-gradient(to bottom, transparent 0%, rgba(204, 204, 204, 0.5) 5%, rgba(204, 204, 204, 0.8) 30%, rgba(204, 204, 204, 1) 50%, rgba(204, 204, 204, 0.8) 70%, rgba(204, 204, 204, 0.5) 90%, transparent 100%);
    z-index: 1;
    width: 2px;
  }
  .gradient-divider-horizontal {
    background: linear-gradient(to right, transparent 0%, rgba(204, 204, 204, 0.5) 15%, rgba(204, 204, 204, 1) 50%, rgba(204, 204, 204, 0.8) 70%, transparent 100%);
    z-index: 1;
    height: 2px;
  }
  .vertical-gradient-text {
    background: rgb(255, 255, 255);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.9416141456582633) 0%, rgba(234, 176, 255, 1) 50%, rgba(56, 156, 255, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    text-shadow: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .comparison-text {
    display: block;
    font-style: italic;
    background: rgb(127, 41, 255);
    background: linear-gradient(0deg, rgba(127, 41, 255, 1) 0%, rgba(45, 94, 255, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    text-shadow: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: 2rem;
    @media (width >= 48rem) {
      width: 4rem;
    }
  }
  @supports not ((background-clip: text) or (-webkit-background-clip: text)) {
    .vertical-gradient-text, .comparison-text {
      color: #ffffff !important;
      background: none !important;
    }
  }
  .geo-text {
    display: inline-block;
    font-weight: 900;
    letter-spacing: 0.05em;
    color: transparent;
    -webkit-text-stroke: 1px #9893af;
    position: relative;
  }
  .text_1 {
    display: inline-block;
    font-weight: 900;
    letter-spacing: 0.05em;
    position: relative;
  }
  .geo-text .first-letter {
    color: #9893af;
    -webkit-text-stroke: 0;
    position: relative;
    z-index: 1;
    background: linear-gradient(180deg, #ffffff 0%, #f5f0ff 70%, #43149a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
@layer utilities {
  .overflow-hidden {
    overflow: hidden;
  }
  .geo-cta-button {
    transform: translateY(0);
    transition: all 0.3s ease;
  }
  .geo-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(147, 51, 234, 0.2);
  }
  .geo-value-card {
    transition: all 0.3s ease;
  }
  .geo-value-card:hover {
    transform: translateY(-2px);
    border-color: #c084fc;
  }
  .service-tab {
    min-width: 12.5rem;
    white-space: nowrap;
  }
  .service-tab.active {
    background: linear-gradient(90deg, rgba(111, 34, 182, 1) 0%, rgba(27, 18, 53, 1) 100%) !important;
    border-color: #8b5cf6 !important;
    color: white !important;
    opacity: 1;
  }
  .service-tab:not(.active) {
    background: #030015 !important;
    border-color: #725e9b !important;
    color: #fff !important;
    opacity: 0.5;
  }
  .service-content {
    backdrop-filter: blur(0.625rem);
    border: 2px solid #725e9b;
  }
  .service-content:hover {
    border-color: rgba(139, 92, 246, 0.5);
  }
  .radial-spotlight {
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 30%, transparent 70%);
  }
  .gradient-overlay-container {
    position: relative;
    overflow: hidden;
  }
  .gradient-overlay-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 20%, rgba(0, 0, 0, 0.8) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.8) 0%, transparent 50%), radial-gradient(circle at 20% 80%, rgba(0, 0, 0, 0.8) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(0, 0, 0, 0.8) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
  }
  .case-card {
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
  }
  .case-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.15);
  }
  .case-card img {
    transition: transform 0.3s ease;
  }
  .case-card:hover img {
    transform: scale(1.05);
  }
  .geo-services-section {
    position: relative;
    overflow: hidden;
  }
  .news-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .news-item:hover {
    transform: translateY(-2px);
  }
  .news-item .flex-shrink-0 {
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  .news-item:hover .flex-shrink-0 {
    transform: scale(1.02);
  }
  .news-item img {
    transition: transform 0.3s ease;
  }
  .news-item:hover img {
    transform: scale(1.1);
  }
  .news-item {
    text-decoration: none;
    color: inherit;
  }
  .news-item:hover {
    text-decoration: none;
    color: inherit;
  }
}
.iconfont-telephone {
  @media (width >= 48rem) {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
}
.rq-telephone {
  @media (width >= 48rem) {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
}
.editor-content {
  color: #e0e0e0;
  line-height: 1.8;
}
.editor-content p {
  margin-bottom: 1.5rem;
  color: #e0e0e0;
  line-height: 1.8;
}
.editor-content p:last-child {
  margin-bottom: 0;
}
.editor-content img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}
.editor-content h1, .editor-content h2, .editor-content h3, .editor-content h4, .editor-content h5, .editor-content h6 {
  color: #ffffff;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}
.editor-content h1 {
  font-size: 2.25rem;
}
.editor-content h2 {
  font-size: 1.875rem;
}
.editor-content h3 {
  font-size: 1.5rem;
}
.editor-content h4 {
  font-size: 1.25rem;
}
.editor-content h5 {
  font-size: 1.125rem;
}
.editor-content h6 {
  font-size: 1rem;
}
.editor-content ul, .editor-content ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
  color: #e0e0e0;
}
.editor-content li {
  margin-bottom: 0.5rem;
  line-height: 1.8;
}
.editor-content blockquote {
  border-left: 4px solid #6b46c1;
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #c0c0c0;
  background: rgba(107, 70, 193, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 8px;
}
.editor-content code {
  background: rgba(107, 70, 193, 0.2);
  color: #e0e0e0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}
.editor-content pre {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #343045;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
  overflow-x: auto;
}
.editor-content pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  color: #e0e0e0;
}
.editor-content a {
  color: #8b5cf6;
  text-decoration: underline;
  transition: color 0.2s ease;
}
.editor-content a:hover {
  color: #a78bfa;
}
.editor-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
}
.editor-content th, .editor-content td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #343045;
}
.editor-content th {
  background: rgba(107, 70, 193, 0.2);
  color: #ffffff;
  font-weight: 600;
}
.editor-content td {
  color: #e0e0e0;
}
.editor-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, #343045, transparent);
  margin: 3rem 0;
}
@media (max-width: 768px) {
  .editor-content {
    font-size: 0.9rem;
  }
  .editor-content h1 {
    font-size: 1.875rem;
  }
  .editor-content h2 {
    font-size: 1.5rem;
  }
  .editor-content h3 {
    font-size: 1.25rem;
  }
  .editor-content img {
    margin: 1.5rem 0;
  }
  .editor-content ul, .editor-content ol {
    padding-left: 1.5rem;
  }
}
.inner-glow {
  box-shadow: inset 0 0 2.5rem rgba(110, 90, 152, 0.3),  inset 0 0 1.875rem rgba(114, 94, 155, 0.2);
}
.bg-custom-gradient {
  background: linear-gradient(0deg, rgba(255,255,255,1) 0%, rgba(234,176,255,1) 24%, rgba(56,156,255,0.7511379551820728) 100%);
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}
